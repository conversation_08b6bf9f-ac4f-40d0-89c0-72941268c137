#include "Demuxer.h"
#include <android/log.h>
#include <thread>

extern "C" {
#include <libavutil/error.h>
#include <libavutil/time.h>
#include <libavutil/pixdesc.h>
}

#define LOG_TAG "Demuxer"
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

// 简化错误处理
static const char* get_error_string(int errnum) {
    static char error_buf[256];
    av_strerror(errnum, error_buf, sizeof(error_buf));
    return error_buf;
}

Demuxer::Demuxer() = default;

Demuxer::~Demuxer() {
    close();
}

int Demuxer::open(const char* url, PacketQueue* videoQueue, PacketQueue* audioQueue) {
    std::lock_guard<std::mutex> lock(mMutex);
    mVideoQueue = videoQueue;
    mAudioQueue = audioQueue;

    mFormatCtx = avformat_alloc_context();
    int ret = avformat_open_input(&mFormatCtx, url, nullptr, nullptr);
    if (ret < 0) {
        LOGE("Failed to open input: %s", get_error_string(ret));
        return ret;
    }

    ret = avformat_find_stream_info(mFormatCtx, nullptr);
    if (ret < 0) {
        LOGE("Failed to find stream info: %s", get_error_string(ret));
        return ret;
    }

    // Dump format information for debugging
    av_dump_format(mFormatCtx, 0, url, 0);

    mVideoStreamIndex = av_find_best_stream(mFormatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
    if (mVideoStreamIndex < 0) {
        LOGE("Failed to find video stream");
    }

    mAudioStreamIndex = av_find_best_stream(mFormatCtx, AVMEDIA_TYPE_AUDIO, -1, mVideoStreamIndex, nullptr, 0);
    if (mAudioStreamIndex < 0) {
        LOGE("Failed to find audio stream");
    }

    LOGD("Demuxer::open() found streams: video_idx=%d, audio_idx=%d", mVideoStreamIndex, mAudioStreamIndex);
    if (mVideoStreamIndex >= 0) {
        AVCodecParameters* codecpar = mFormatCtx->streams[mVideoStreamIndex]->codecpar;
        LOGD("Video stream codec_id: %d (%s)",
             codecpar->codec_id,
             avcodec_get_name(codecpar->codec_id));
        LOGD("Video stream raw codec_id: 0x%08x", codecpar->codec_id);
        LOGD("Video resolution: %dx%d", codecpar->width, codecpar->height);
        LOGD("Video pixel format: %s", av_get_pix_fmt_name((AVPixelFormat)codecpar->format));

        // 检查是否是已知的问题编解码器ID
        if (codecpar->codec_id == -1275068297) {
            LOGD("Detected problematic codec_id, this appears to be a byte order issue");
        }
    }

    if (mVideoStreamIndex < 0 && mAudioStreamIndex < 0) {
        LOGE("Failed to find any video or audio stream");
        return -1;
    }

    mDuration = (double)mFormatCtx->duration / AV_TIME_BASE;
    return 0;
}

void Demuxer::close() {
    stop();
    std::lock_guard<std::mutex> lock(mMutex);
    if (mFormatCtx) {
        avformat_close_input(&mFormatCtx);
        mFormatCtx = nullptr;
    }
}

void Demuxer::start() {
    mDemuxThread = new std::thread(&Demuxer::demux, this);
}

void Demuxer::stop() {
    mAbort = true;
    if (mDemuxThread) {
        mDemuxThread->join();
        delete mDemuxThread;
        mDemuxThread = nullptr;
    }
}

void Demuxer::demux() {
    LOGD("Demux thread started");
    while (!mAbort) {
        AVPacket* packet = av_packet_alloc();
        int ret = av_read_frame(mFormatCtx, packet);
        if (ret < 0) {
            av_packet_free(&packet);
            LOGD("End of file or error in demuxing");
            break;
        }

        if (packet->stream_index == mVideoStreamIndex) {
            mVideoQueue->push(packet);
            // Update current PTS based on video packets (assume time_base of stream)
            mCurrentPts = packet->pts * av_q2d(mFormatCtx->streams[mVideoStreamIndex]->time_base);
        } else if (packet->stream_index == mAudioStreamIndex) {
            mAudioQueue->push(packet);
            // Alternatively update from audio stream if video absent
            if (mVideoStreamIndex == -1) {
                mCurrentPts = packet->pts * av_q2d(mFormatCtx->streams[mAudioStreamIndex]->time_base);
            }
        } else {
            av_packet_free(&packet);
        }
    }
    LOGD("Demuxing finished");
}

AVStream* Demuxer::getVideoStream() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mVideoStreamIndex != -1) {
        return mFormatCtx->streams[mVideoStreamIndex];
    }
    return nullptr;
}

AVStream* Demuxer::getAudioStream() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mAudioStreamIndex != -1) {
        return mFormatCtx->streams[mAudioStreamIndex];
    }
    return nullptr;
}

AVCodecParameters* Demuxer::getVideoCodecParams() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mVideoStreamIndex != -1) {
        return mFormatCtx->streams[mVideoStreamIndex]->codecpar;
    }
    return nullptr;
}

AVCodecParameters* Demuxer::getAudioCodecParams() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mAudioStreamIndex != -1) {
        return mFormatCtx->streams[mAudioStreamIndex]->codecpar;
    }
    return nullptr;
}

double Demuxer::getDuration() const {
    return mDuration;
}

int Demuxer::seek(int64_t positionUs) {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mFormatCtx) return -1;

    // Clear internal queues so that old packets are dropped
    if (mVideoQueue) mVideoQueue->clear();
    if (mAudioQueue) mAudioQueue->clear();

    int ret = av_seek_frame(mFormatCtx, -1, positionUs, AVSEEK_FLAG_BACKWARD);
    if (ret < 0) {
        LOGE("Failed to seek: %s", get_error_string(ret));
        return ret;
    }

    // After seeking, flush internal buffers so demuxing starts at correct position
    avformat_flush(mFormatCtx);
    mCurrentPts = (double)positionUs / AV_TIME_BASE;
    return 0;
}

double Demuxer::getCurrentPts() {
    return mCurrentPts;
}