                        -HD:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=30
-D<PERSON>DROID_PLATFORM=android-30
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\APP\Android-studio\Android\SDK\ndk\21.3.6528147
-DCMAKE_ANDROID_NDK=D:\APP\Android-studio\Android\SDK\ndk\21.3.6528147
-DCMAKE_TOOLCHAIN_FILE=D:\APP\Android-studio\Android\SDK\ndk\21.3.6528147\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\APP\Android-studio\Android\SDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\build\intermediates\cxx\RelWithDebInfo\5v2y2j1a\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\build\intermediates\cxx\RelWithDebInfo\5v2y2j1a\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\.cxx\RelWithDebInfo\5v2y2j1a\arm64-v8a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2