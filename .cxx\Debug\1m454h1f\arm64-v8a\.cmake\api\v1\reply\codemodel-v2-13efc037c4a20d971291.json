{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "androidplayer", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "androidplayer::@6890427a1f51a3e7e1df", "jsonFile": "target-androidplayer-Debug-b168d365d6b3e4fd9d9d.json", "name": "androidplayer", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/Debug/1m454h1f/arm64-v8a", "source": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp"}, "version": {"major": 2, "minor": 3}}