/*
 * AAudioRender.cpp
 *
 * 基于 AAudio 的音频渲染器实现。
 * 功能：
 *   - 创建低延迟输出流
 *   - 支持播放/暂停/刷新
 *   - 通过用户提供的回调函数填充音频数据
 *
 * 生命周期：
 *   1. configure(...)  设置采样率/通道数/格式
 *   2. setCallback(...)注册数据回调
 *   3. start()         打开并启动 AAudio 流
 *   4. pause(bool)     暂停或继续播放
 *   5. flush()         暂停->清空缓冲区->重新启动
 *   6. 析构函数        自动关闭流
 */

#include "AAudioRender.h"
#include "log.h"

#define LOG_TAG "AAudioRender"

/* ------------------- 构造函数 ------------------- */
AAudioRender::AAudioRender() {
    this->paused = false;                // 初始未暂停
    this->sample_rate = 44100;           // 默认采样率 44.1 kHz
    this->channel_count = 2;             // 默认双声道
    this->format = AAUDIO_FORMAT_PCM_I16;// 默认 16-bit 有符号 PCM
    this->callback = nullptr;            // 尚未注册回调
}

/* ------------------- 析构函数 ------------------- */
AAudioRender::~AAudioRender() {
    // 关闭并释放 AAudio 流资源
    AAudioStream_close(stream);
}

/* ------------------- 启动音频流 ------------------- */
int AAudioRender::start() {
    AAudioStreamBuilder *builder;

    /* 1. 创建构建器 */
    aaudio_result_t result = AAudio_createStreamBuilder(&builder);
    if (result != AAUDIO_OK) {
        LOGE(LOG_TAG, "createStreamBuilder failed: %s", AAudio_convertResultToText(result));
        return -1;
    }

    /* 2. 配置流参数 */
    AAudioStreamBuilder_setSampleRate(builder, this->sample_rate);
    AAudioStreamBuilder_setChannelCount(builder, this->channel_count);
    AAudioStreamBuilder_setFormat(builder, this->format);
    AAudioStreamBuilder_setPerformanceMode(builder, AAUDIO_PERFORMANCE_MODE_LOW_LATENCY); // 低延迟
    AAudioStreamBuilder_setSharingMode(builder, AAUDIO_SHARING_MODE_SHARED);              // 共享模式

    /* 3. 必须提供数据回调，否则无法填充音频数据 */
    if (!this->callback) {
        LOGE(LOG_TAG, "callback is nullptr");
        return -1;
    }
    AAudioStreamBuilder_setDataCallback(builder, callback, user_data);

    /* 4. 打开流 */
    result = AAudioStreamBuilder_openStream(builder, &stream);
    if (result != AAUDIO_OK) {
        LOGE(LOG_TAG, "openStream failed: %s", AAudio_convertResultToText(result));
        return -1;
    }

    /* 5. 用实际打开的流参数更新成员变量（可能与请求值不同） */
    this->format = AAudioStream_getFormat(stream);
    this->channel_count = AAudioStream_getChannelCount(stream);
    this->sample_rate = AAudioStream_getSampleRate(stream);

    /* 6. 启动流，开始回调 */
    result = AAudioStream_requestStart(stream);
    if (result != AAUDIO_OK) {
        LOGE(LOG_TAG, "requestStart failed: %s", AAudio_convertResultToText(result));
        return -1;
    }

    /* 7. 构建器已无用，释放内存 */
    AAudioStreamBuilder_delete(builder);
    return 0;
}

/* ------------------- 刷新缓冲区 ------------------- */
int AAudioRender::flush() {
    const int64_t timeout = 100000000; // 100 毫秒 (单位纳秒)

    /* 1. 请求暂停并等待真正暂停 */
    AAudioStream_requestPause(stream);
    aaudio_result_t result = AAUDIO_OK;
    aaudio_stream_state_t currentState = AAudioStream_getState(stream);
    aaudio_stream_state_t inputState = currentState;
    while (result == AAUDIO_OK && currentState != AAUDIO_STREAM_STATE_PAUSED) {
        result = AAudioStream_waitForStateChange(
                stream, inputState, &currentState, timeout);
        inputState = currentState;
    }

    /* 2. 清空内部缓冲区 */
    AAudioStream_requestFlush(stream);

    /* 3. 重新启动流并等待真正启动 */
    AAudioStream_requestStart(stream);
    result = AAUDIO_OK;
    currentState = AAudioStream_getState(stream);
    inputState = currentState;
    while (result == AAUDIO_OK && currentState != AAUDIO_STREAM_STATE_STARTED) {
        result = AAudioStream_waitForStateChange(
                stream, inputState, &currentState, timeout);
        inputState = currentState;
    }
    return result; // 返回最终状态码
}

/* ------------------- 暂停/继续 ------------------- */
int AAudioRender::pause(bool p) {
    /* 如果当前状态与目标状态一致，直接返回成功 */
    if (p == paused) {
        return 0;
    }

    const int64_t timeout = 100000000; // 100 毫秒

    if (p) {
        /* ---- 进入暂停 ---- */
        AAudioStream_requestPause(stream);
        aaudio_result_t result = AAUDIO_OK;
        aaudio_stream_state_t currentState = AAudioStream_getState(stream);
        aaudio_stream_state_t inputState = currentState;
        while (result == AAUDIO_OK && currentState != AAUDIO_STREAM_STATE_PAUSED) {
            result = AAudioStream_waitForStateChange(
                    stream, inputState, &currentState, timeout);
            inputState = currentState;
        }
        paused = true;
        return result;
    } else {
        /* ---- 恢复播放 ---- */
        AAudioStream_requestStart(stream);
        aaudio_result_t result = AAUDIO_OK;
        aaudio_stream_state_t currentState = AAudioStream_getState(stream);
        aaudio_stream_state_t inputState = currentState;
        while (result == AAUDIO_OK && currentState != AAUDIO_STREAM_STATE_STARTED) {
            result = AAudioStream_waitForStateChange(
                    stream, inputState, &currentState, timeout);
            inputState = currentState;
        }
        paused = false;
        return result;
    }
}

/* ------------------- 注册数据回调 ------------------- */
void AAudioRender::setCallback(AAudioCallback cb, void* data) {
    this->callback  = cb;      // 数据回调函数指针
    this->user_data = data;    // 透传给回调的自定义数据
}

/* ------------------- 配置音频参数 ------------------- */
void AAudioRender::configure(int32_t sampleRate,
                             int32_t channelCnt,
                             aaudio_format_t fmt) {
    this->sample_rate  = sampleRate;
    this->channel_count = channelCnt;
    this->format        = fmt;
}