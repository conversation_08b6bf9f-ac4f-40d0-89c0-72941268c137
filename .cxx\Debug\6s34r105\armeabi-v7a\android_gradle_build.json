{"buildFiles": ["/home/<USER>/projects/androidplayer/app/src/main/cpp/CMakeLists.txt"], "cleanCommandsComponents": [["/usr/bin/ninja", "-C", "/home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/usr/bin/ninja", "-C", "/home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"androidplayer::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "androidplayer", "output": "/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.so", "runtimeFiles": ["/home/<USER>/projects/androidplayer/app/src/main/jniLibs/armeabi-v7a/libffmpeg.so", "/home/<USER>/projects/androidplayer/app/src/main/jniLibs/armeabi-v7a/libffmpeg.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}