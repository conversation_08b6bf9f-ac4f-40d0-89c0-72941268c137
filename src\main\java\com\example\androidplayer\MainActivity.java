package com.example.androidplayer;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.widget.Button;
import android.widget.SeekBar;

import com.example.androidplayer.databinding.ActivityMainBinding;

/**
 * 主界面 Activity：负责播放本地 MP4 文件，提供播放/暂停、停止、倍速切换、进度条拖拽等功能。
 *
 * 1. 权限：Android 11+ 需申请“所有文件访问”权限（MANAGE_EXTERNAL_STORAGE）。
 * 2. 视频路径：固定为 /sdcard/test12.mp4。
 * 3. 使用 JNI 封装的 Player 类进行底层解码与渲染。
 * 4. 进度更新线程每 500ms 刷新一次 SeekBar。
 * 5. 所有 UI 操作均通过主线程 Handler 完成，避免 ANR。
 */
public class MainActivity extends AppCompatActivity {

    // 加载 native 库 libandroidplayer.so，库中实现 Player 的 JNI 方法
    // FFmpeg 库现在通过 CMake 自动链接，无需手动加载
    static {
        System.loadLibrary("androidplayer");
    }

    /** 与 JNI 交互的播放器实例 */
    private Player player;

    /** 主线程 Handler，用于把子线程的进度同步到 UI */
    private Handler mHandler;

    /** 进度条 */
    private SeekBar mSeekBar;

    /** 进度更新线程 */
    private Thread progressThread;

    /** 控制进度线程的循环 */
    private volatile boolean isRunning = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Android 11 及以上版本需要手动申请“管理所有文件”权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                // 跳转到系统设置页让用户手动授予权限
                startActivity(new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION));
            }
        }

        // 使用 ViewBinding 获取布局根视图
        setContentView(ActivityMainBinding.inflate(getLayoutInflater()).getRoot());

        // 找到布局中的 SeekBar
        mSeekBar = findViewById(R.id.seekBar);

        /* ------------------- 创建主线程 Handler ------------------- */
        mHandler = new Handler(Looper.getMainLooper()) {
            /**
             * 子线程通过 sendMessage 把当前播放进度发送到主线程，
             * Handler 收到消息后在主线程更新 SeekBar。
             */
            @Override
            public void handleMessage(@NonNull Message msg) {
                super.handleMessage(msg);
                if (msg.what == 1) { // 约定 what = 1 表示更新进度
                    Bundle bundle = msg.getData();
                    int progress = bundle.getInt("progress");
                    mSeekBar.setProgress(progress); // 必须在主线程执行
                }
            }
        };

        /* ------------------- 初始化播放器 ------------------- */
        player = new Player();                      // 创建 JNI 播放器

        // 使用已知的测试文件路径
        String testFilePath = "file:/sdcard/test.mp4";
        Log.d("MainActivity", "使用视频文件: " + testFilePath);
        player.setDataSource(testFilePath); // 设置数据源

        /* ------------------- 关联 SurfaceView ------------------- */
        ((SurfaceView) findViewById(R.id.surfaceView))
                .getHolder()
                .addCallback(new SurfaceHolder.Callback() {
                    /**
                     * Surface 创建成功后立即将其绑定给底层播放器，
                     * 之后才能进行解码渲染。
                     */
                    @Override
                    public void surfaceCreated(@NonNull SurfaceHolder holder) {
                        player.setSurface(holder.getSurface());
                    }

                    @Override
                    public void surfaceChanged(@NonNull SurfaceHolder holder,
                                               int format, int width, int height) {
                        // 暂不处理尺寸变化
                    }

                    @Override
                    public void surfaceDestroyed(@NonNull SurfaceHolder holder) {
                        // 可在播放器内部处理 Surface 销毁
                    }
                });

        /* ------------------- 进度更新线程 ------------------- */
        // 注意：该线程一旦 start() 之后不会自动退出，属于常驻线程
        progressThread = new Thread(() -> {
            int progress;
            while (isRunning) { // 使用 isRunning 控制循环
                // 从播放器获取当前进度（0~1），乘以 100 后取整
                progress = (int) Math.round(player.getProgress() * 100);
                setSeekBar(progress); // 通过 Handler 发送到主线程
                try {
                    Thread.sleep(200); // 每 200ms 更新一次，提高响应性
                } catch (InterruptedException e) {
                    // 线程被中断，安全退出循环
                    break;
                }
            }
        });

        /* ------------------- 播放/暂停按钮 ------------------- */
        Button play = findViewById(R.id.button);
        play.setText("播放");
        play.setOnClickListener(v -> {
            Player.PlayerState state = player.getState();
            if (state == Player.PlayerState.Playing) {
                // 如果正在播放，就暂停
                player.pause(true);
                play.setText("播放");
            } else {
                // 如果是其他状态（None, Paused, End），就开始或恢复播放
                if (state == Player.PlayerState.None || state == Player.PlayerState.End) {
                    player.start();
                    if (!progressThread.isAlive()) {
                        progressThread.start(); // 仅在第一次或结束后播放时启动进度线程
                    }
                } else { // Paused 状态
                    player.pause(false); // 恢复播放
                }
                play.setText("暂停");
            }
        });

        /* ------------------- 停止按钮 ------------------- */
        Button stop = findViewById(R.id.button2);
        stop.setOnClickListener(v -> {
            player.stop();     // 停止播放
            play.setText("播放");
            setSeekBar(0);     // 进度条归零
        });

        /* ------------------- 倍速按钮 ------------------- */
        Button speed = findViewById(R.id.button3);
        speed.setText("1.0x");   // 初始倍速
        speed.setOnClickListener(v -> {
            // 按 1.0x -> 1.5x -> 2.0x -> 0.5x -> 1.0x 循环
            switch (speed.getText().toString()) {
                case "1.0x":
                    player.setSpeed(1.5f); // 1.5 倍速
                    speed.setText("1.5x");
                    break;
                case "1.5x":
                    player.setSpeed(2.0f); // 2.0 倍速
                    speed.setText("2.0x");
                    break;
                case "2.0x":
                    player.setSpeed(0.5f); // 0.5 倍速
                    speed.setText("0.5x");
                    break;
                case "0.5x":
                    player.setSpeed(1.0f); // 恢复 1.0 倍速
                    speed.setText("1.0x");
                    break;
                default:
                    player.setSpeed(1.0f); // 默认恢复 1.0 倍速
                    speed.setText("1.0x");
                    break;
            }
        });

        /* ------------------- SeekBar 拖拽监听 ------------------- */
        mSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                // 在拖动过程中，我们不执行任何操作，以避免性能问题
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                // 用户开始拖动时，可以暂停播放以获得更好的体验，这里暂时不实现
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // 仅在用户停止拖动时，才执行 seek 操作
                if (player.getState() != Player.PlayerState.None) {
                    double position = (double) seekBar.getProgress() / 100.0;
                    player.seek(position);
                }
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        isRunning = false; // 通知线程停止循环
        if (progressThread != null) {
            progressThread.interrupt(); // 中断线程的 sleep 状态
        }
        player.stop(); // 确保播放器资源被释放
    }

    /**
     * 供子线程调用，把当前播放进度通过 Handler 发送到主线程更新 SeekBar。
     * @param progress 0~100 之间的整数
     */
    private void setSeekBar(int progress) {
        Bundle bundle = new Bundle();
        bundle.putInt("progress", progress);
        Message msg = new Message();
        msg.setData(bundle);
        msg.what = 1; // 区分消息类型
        mHandler.sendMessage(msg);
    }
}
