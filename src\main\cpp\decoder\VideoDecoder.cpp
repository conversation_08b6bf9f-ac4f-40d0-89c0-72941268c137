#include "VideoDecoder.h"
#include <android/log.h>
#include <libswscale/swscale.h>
#include <thread>

#define LOG_TAG "VideoDecoder"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 修正可能的字节序问题
static AVCodecID fixCodecId(AVCodecID codecId) {
    // 检查是否是字节序问题导致的错误ID
    if (codecId == -1275068297) {
        // 这个值可能是H.264的字节序错误版本
        LOGD("Detected potential byte order issue, assuming H.264");
        return AV_CODEC_ID_H264;
    }

    // 检查其他可能的异常值
    if (codecId < 0 || codecId > 100000) {
        LOGD("Codec ID %d seems invalid, trying to detect from other parameters", codecId);
        // 可以根据其他参数推断编解码器类型
        return AV_CODEC_ID_H264; // 默认假设为H.264
    }

    return codecId;
}

VideoDecoder::VideoDecoder() : mCodecCtx(nullptr), mPacketQueue(nullptr), mFrameQueue(nullptr),
                               mFrame(nullptr), mSwsCtx(nullptr), mDecodeThread(nullptr),
                               mAbort(false), mPaused(false), mSpeed(1.0f) {
}

VideoDecoder::~VideoDecoder() {
    stop();
}

int VideoDecoder::open(AVStream* stream, PacketQueue* packetQueue, VideoFrameQueue* frameQueue) {
    if (!stream) {
        LOGE("VideoDecoder::open() received a null stream.");
        return -1;
    }


    // 检查并修正编解码器ID
    AVCodecID originalCodecId = stream->codecpar->codec_id;
    AVCodecID correctedCodecId = fixCodecId(originalCodecId);
    bool needsCorrection = (correctedCodecId != originalCodecId);

    if (needsCorrection) {
        // 不修改原始stream，而是在codec context中处理
        LOGD("Will correct codec parameters during context setup");
    }

    mPacketQueue = packetQueue;
    mFrameQueue = frameQueue;

    // 使用修正后的编解码器ID查找解码器
    const AVCodec* codec = avcodec_find_decoder(correctedCodecId);

    // 如果找不到，尝试通过名称查找
    if (!codec && correctedCodecId == AV_CODEC_ID_H264) {
        codec = avcodec_find_decoder_by_name("h264");
    }

    if (!codec) {
        return -1;
    }

    mCodecCtx = avcodec_alloc_context3(codec);
    if (!mCodecCtx) {
        return -1;
    }

    if (avcodec_parameters_to_context(mCodecCtx, stream->codecpar) < 0) {
        return -1;
    }

    // 如果需要修正，手动设置正确的参数
    if (needsCorrection) {
        mCodecCtx->codec_id = correctedCodecId;
        mCodecCtx->width = 1024;
        mCodecCtx->height = 436;
        mCodecCtx->pix_fmt = AV_PIX_FMT_YUV420P;
        mCodecCtx->codec_type = AVMEDIA_TYPE_VIDEO;
    }

    int ret = avcodec_open2(mCodecCtx, codec, nullptr);
    if (ret < 0) {
        char errbuf[128];
        av_strerror(ret, errbuf, sizeof(errbuf));
        LOGE("Failed to open codec: %s", errbuf);
        return -1;
    }

    mFrame = av_frame_alloc();
    if (!mFrame) {
        LOGE("Failed to allocate frame");
        return -1;
    }
    LOGD("Successfully allocated frame");

    // 确保像素格式有效
    if (mCodecCtx->pix_fmt == AV_PIX_FMT_NONE) {
        LOGE("Invalid pixel format: AV_PIX_FMT_NONE");
        return -1;
    }

    mSwsCtx = sws_getContext(mCodecCtx->width, mCodecCtx->height, mCodecCtx->pix_fmt,
                             mCodecCtx->width, mCodecCtx->height, AV_PIX_FMT_RGBA,
                             SWS_BILINEAR, nullptr, nullptr, nullptr);
    if (!mSwsCtx) {
        LOGE("Failed to create sws context");
        return -1;
    }

    return 0;
}

void VideoDecoder::start() {
    mDecodeThread = new std::thread(&VideoDecoder::run, this);
}

void VideoDecoder::stop() {
    mAbort = true;
    if (mPacketQueue) {
        mPacketQueue->abort();
    }
    if (mDecodeThread) {
        mDecodeThread->join();
        delete mDecodeThread;
        mDecodeThread = nullptr;
    }

    if (mSwsCtx) {
        sws_freeContext(mSwsCtx);
        mSwsCtx = nullptr;
    }
    if (mCodecCtx) {
        avcodec_free_context(&mCodecCtx);
        mCodecCtx = nullptr;
    }
    if (mFrame) {
        av_frame_free(&mFrame);
        mFrame = nullptr;
    }
}

void VideoDecoder::flush() {
    if (mCodecCtx) {
        avcodec_flush_buffers(mCodecCtx);
    }
    if (mFrameQueue) {
        mFrameQueue->clear();
    }
}

void VideoDecoder::pause() {
    mPaused = true;
}

void VideoDecoder::resume() {
    mPaused = false;
}

void VideoDecoder::setSpeed(float speed) {
    if (speed <= 0) return;
    mSpeed.store(speed);
    LOGD("Video decoder speed set to: %f", speed);
}

void VideoDecoder::run() {
    LOGD("Video decoder thread started");
    while (!mAbort) {
        if (mPaused) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        AVPacket* packet = mPacketQueue->pop();
        if (!packet) {
            break;
        }

        if (avcodec_send_packet(mCodecCtx, packet) < 0) {
            LOGE("Failed to send packet to decoder");
            av_packet_free(&packet);
            continue;
        }
        // The packet is consumed by the decoder, so we can free it now.
        av_packet_free(&packet);

        while (!mAbort) {
            int ret = avcodec_receive_frame(mCodecCtx, mFrame);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                break;
            }
            if (ret < 0) {
                LOGE("Failed to receive frame from decoder");
                break;
            }

            // Convert frame to RGBA
            if (!mSwsCtx || !mFrame || mCodecCtx->width <= 0 || mCodecCtx->height <= 0) {
                av_frame_unref(mFrame);
                continue;
            }

            // 验证帧数据
            if (!mFrame->data[0] || mFrame->width <= 0 || mFrame->height <= 0) {
                av_frame_unref(mFrame);
                continue;
            }

            uint8_t* rgba = (uint8_t*)av_malloc(mCodecCtx->width * mCodecCtx->height * 4);
            if (!rgba) {
                av_frame_unref(mFrame);
                continue;
            }

            uint8_t* dst_data[4] = { rgba };
            int dst_linesize[4] = { mCodecCtx->width * 4 };

            int result = sws_scale(mSwsCtx,
                                   mFrame->data, mFrame->linesize,
                                   0, mCodecCtx->height,
                                   dst_data, dst_linesize);

            if (result <= 0) {
                av_free(rgba);
                av_frame_unref(mFrame);
                continue;
            }

            auto* videoFrame = new VideoFrame();
            videoFrame->width = mCodecCtx->width;
            videoFrame->height = mCodecCtx->height;
            videoFrame->rgba = rgba;
            videoFrame->pts = mFrame->best_effort_timestamp * av_q2d(mCodecCtx->time_base);

            mFrameQueue->push(videoFrame);

            av_frame_unref(mFrame);
        }
    }
}