{"artifacts": [{"path": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/5v2y2j1a/obj/arm64-v8a/libandroidplayer.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 69, "parent": 0}, {"command": 1, "file": 0, "line": 83, "parent": 0}, {"command": 2, "file": 0, "line": 14, "parent": 0}, {"command": 2, "file": 0, "line": 15, "parent": 0}, {"command": 2, "file": 0, "line": 16, "parent": 0}, {"command": 3, "file": 0, "line": 20, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC"}, {"fragment": "-std=c++11"}], "defines": [{"backtrace": 3, "define": "__STDC_CONSTANT_MACROS"}, {"backtrace": 4, "define": "__STDC_FORMAT_MACROS"}, {"backtrace": 5, "define": "__STDC_LIMIT_MACROS"}, {"define": "androidplayer_EXPORTS"}], "includes": [{"backtrace": 6, "path": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "sysroot": {"path": "D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "androidplayer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libgcc_real.a -Wl,--exclude-libs,libatomic.a -Wl,--build-id -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"fragment": "-LD:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\..\\jniLibs\\arm64-v8a", "role": "libraryPath"}, {"backtrace": 2, "fragment": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\30\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "-<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 2, "fragment": "-lGLESv2", "role": "libraries"}, {"fragment": "-lavu<PERSON>", "role": "libraries"}, {"fragment": "-lswresample", "role": "libraries"}, {"fragment": "-lswscale", "role": "libraries"}, {"fragment": "-lavcodec", "role": "libraries"}, {"fragment": "-lavformat", "role": "libraries"}, {"fragment": "-lavfilter", "role": "libraries"}, {"fragment": "-lavde<PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lz", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "androidplayer", "nameOnDisk": "libandroidplayer.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "AAudioRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ANWRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "native-lib.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/YuvDumper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "queue/PacketQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "queue/VideoFrameQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "demuxer/Demuxer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/VideoDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/AudioDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "render/GLRenderer.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}