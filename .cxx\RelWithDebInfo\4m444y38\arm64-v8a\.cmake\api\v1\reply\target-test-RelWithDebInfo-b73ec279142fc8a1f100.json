{"artifacts": [{"path": "/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/4m444y38/obj/arm64-v8a/test"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 1, "file": 0, "line": 46, "parent": 0}, {"command": 2, "file": 0, "line": 7, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIE"}], "includes": [{"backtrace": 3, "path": "/home/<USER>/projects/androidplayer/app/src/main/cpp/include"}], "language": "CXX", "sourceIndexes": [0], "sysroot": {"path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot"}}], "dependencies": [{"backtrace": 2, "id": "androidplayer::@6890427a1f51a3e7e1df"}], "id": "test::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG", "role": "flags"}, {"fragment": "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments -Wl,--gc-sections", "role": "flags"}, {"backtrace": 2, "fragment": "/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/4m444y38/obj/arm64-v8a/libandroidplayer.so", "role": "libraries"}, {"backtrace": 2, "fragment": "/home/<USER>/projects/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libffmpeg.so", "role": "libraries"}, {"backtrace": 2, "fragment": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/33/liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 2, "fragment": "-lGLESv2", "role": "libraries"}, {"fragment": "-<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 2, "fragment": "-lGLESv2", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot"}}, "name": "test", "nameOnDisk": "test", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test/test.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}