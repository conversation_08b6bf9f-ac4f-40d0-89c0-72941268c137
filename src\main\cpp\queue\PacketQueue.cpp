#include "PacketQueue.h"

PacketQueue::PacketQueue() : mAbort(false) {
}

PacketQueue::~PacketQueue() {
    clear();
}

void PacketQueue::push(AVPacket *packet) {
    std::unique_lock<std::mutex> lock(mMutex);
    if (mAbort) {
        return;
    }
    mQueue.push(packet);
    mCond.notify_one();
}

AVPacket* PacketQueue::pop() {
    std::unique_lock<std::mutex> lock(mMutex);
    mCond.wait(lock, [this] { return !mQueue.empty() || mAbort; });

    if (mAbort) {
        return nullptr;
    }

    AVPacket *packet = mQueue.front();
    mQueue.pop();
    return packet;
}

void PacketQueue::clear() {
    std::lock_guard<std::mutex> lock(mMutex);
    while (!mQueue.empty()) {
        AVPacket *packet = mQueue.front();
        av_packet_free(&packet);
        mQueue.pop();
    }
}

void PacketQueue::abort() {
    std::unique_lock<std::mutex> lock(mMutex);
    mAbort = true;
    mCond.notify_all();
}

int PacketQueue::size() {
    return mQueue.size();
}
