{"buildFiles": ["D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\APP\\Android-studio\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\.cxx\\Debug\\1m454h1f\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\APP\\Android-studio\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\.cxx\\Debug\\1m454h1f\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"androidplayer::@6890427a1f51a3e7e1df": {"artifactName": "androidplayer", "abi": "arm64-v8a", "output": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\build\\intermediates\\cxx\\Debug\\1m454h1f\\obj\\arm64-v8a\\libandroidplayer.so", "runtimeFiles": []}}}