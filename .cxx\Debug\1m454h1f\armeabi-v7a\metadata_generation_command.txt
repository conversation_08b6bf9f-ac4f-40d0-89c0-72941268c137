                        -HC:\Users\<USER>\Desktop\androidplayer\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=30
-DANDROID_PLATFORM=android-30
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\21.3.6528147
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\21.3.6528147
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\21.3.6528147\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\androidplayer\app\build\intermediates\cxx\Debug\1m454h1f\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\androidplayer\app\build\intermediates\cxx\Debug\1m454h1f\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\Desktop\androidplayer\app\.cxx\Debug\1m454h1f\armeabi-v7a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2