#ifndef ANDROIDPLAYER_RINGBUFFER_H
#define ANDROIDPLAYER_RINGBUFFER_H

#include <vector>
#include <mutex>
#include <condition_variable>
#include <cstring>
#include <algorithm>

class RingBuffer {
public:
    explicit RingBuffer(size_t capacity) :
        m_capacity(capacity),
        m_buffer(capacity),
        m_head(0),
        m_tail(0),
        m_size(0) {}

    ~RingBuffer() = default;

    // 写入数据
    size_t write(const uint8_t *data, size_t bytes) {
        if (data == nullptr || bytes == 0) {
            return 0;
        }

        std::unique_lock<std::mutex> lock(m_mutex);
        m_cond_not_full.wait(lock, [this] { return m_size < m_capacity; });

        size_t bytes_to_write = std::min(bytes, m_capacity - m_size);
        if (bytes_to_write == 0) {
            return 0;
        }

        size_t first_chunk_size = std::min(bytes_to_write, m_capacity - m_tail);
        memcpy(&m_buffer[m_tail], data, first_chunk_size);

        if (first_chunk_size < bytes_to_write) {
            memcpy(&m_buffer[0], data + first_chunk_size, bytes_to_write - first_chunk_size);
        }

        m_tail = (m_tail + bytes_to_write) % m_capacity;
        m_size += bytes_to_write;

        lock.unlock();
        m_cond_not_empty.notify_all();

        return bytes_to_write;
    }

    // 读取数据
    size_t read(uint8_t *data, size_t bytes) {
        if (data == nullptr || bytes == 0) {
            return 0;
        }

        std::unique_lock<std::mutex> lock(m_mutex);
        m_cond_not_empty.wait(lock, [this] { return m_size > 0; });

        size_t bytes_to_read = std::min(bytes, m_size);
        if (bytes_to_read == 0) {
            return 0;
        }

        size_t first_chunk_size = std::min(bytes_to_read, m_capacity - m_head);
        memcpy(data, &m_buffer[m_head], first_chunk_size);

        if (first_chunk_size < bytes_to_read) {
            memcpy(data + first_chunk_size, &m_buffer[0], bytes_to_read - first_chunk_size);
        }

        m_head = (m_head + bytes_to_read) % m_capacity;
        m_size -= bytes_to_read;

        lock.unlock();
        m_cond_not_full.notify_all();

        return bytes_to_read;
    }

    size_t size() {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_size;
    }

    size_t capacity() const {
        return m_capacity;
    }

    void flush() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_head = 0;
        m_tail = 0;
        m_size = 0;
        m_cond_not_full.notify_all();
    }

private:
    size_t m_capacity;
    std::vector<uint8_t> m_buffer;
    size_t m_head;
    size_t m_tail;
    size_t m_size;

    std::mutex m_mutex;
    std::condition_variable m_cond_not_full;
    std::condition_variable m_cond_not_empty;
};

#endif //ANDROIDPLAYER_RINGBUFFER_H
