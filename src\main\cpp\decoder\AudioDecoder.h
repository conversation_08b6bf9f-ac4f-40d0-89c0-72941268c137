#ifndef ANDROIDPLAYER_AUDIODECODER_H
#define ANDROIDPLAYER_AUDIODECODER_H

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libswresample/swresample.h>
};

#include "../utils/RingBuffer.h"
#include "../queue/PacketQueue.h"
#include <thread>
#include <atomic>

class AudioDecoder {
public:
    AudioDecoder();
    ~AudioDecoder();

    bool open(AVStream *audio_stream, PacketQueue* packetQueue, RingBuffer *ring_buffer);
    void start();
    void stop();
    void flush();

    int getSampleRate() const { return m_out_sample_rate; }
    int getChannels() const { return m_out_channels; }
    AVSampleFormat getSampleFormat() const { return m_out_sample_fmt; }
    void setSpeed(float speed);

private:
    void run();
    void close();

    AVCodecContext *m_codec_ctx = nullptr;
    AVFrame *m_frame = nullptr;
    SwrContext *m_swr_ctx = nullptr;
    PacketQueue *m_packet_queue = nullptr;
    RingBuffer *m_ring_buffer = nullptr;
    std::thread* m_decode_thread = nullptr;
    volatile bool m_abort = false;

    uint8_t *m_out_buffer = nullptr;
    int m_buffer_size = 0;
    int m_out_channels;
    int m_out_sample_rate;
    AVSampleFormat m_out_sample_fmt;
    std::atomic<float> m_speed{1.0f};
};

#endif //ANDROIDPLAYER_AUDIODECODER_H
