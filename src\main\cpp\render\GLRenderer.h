// GLRenderer.h
#pragma once
#include <android/native_window.h>
#include <GLES2/gl2.h>
#include <EGL/egl.h> 
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>
#include "../queue/VideoFrameQueue.h"

class GLRenderer {
public:
    GLRenderer(ANativeWindow* window, VideoFrameQueue* frameQueue);
    ~GLRenderer();

    bool init(ANativeWindow* window, int width, int height);
    void updateWindow(ANativeWindow* window);   // 新增
    void renderFrame(const uint8_t* rgba, int width, int height);
    void release();

    void start();
    void stop();
    void resetTiming(); // 重置时间控制
    void setSpeed(float speed); // 设置播放速度

private:
    void renderLoop();
    bool initEGL();
    void releaseEGL();

    ANativeWindow* mWindow = nullptr;
    VideoFrameQueue* mFrameQueue = nullptr;
    std::thread* mRenderThread = nullptr;
    std::atomic<bool> mRunning{false};
    std::mutex mMutex;

    EGLDisplay mDisplay = EGL_NO_DISPLAY;
    EGLSurface mSurface = EGL_NO_SURFACE;
    EGLContext mContext = EGL_NO_CONTEXT;
    GLuint mProgram = 0;
    GLuint mTexture = 0;

    // 添加时间控制
    double mStartTime = 0.0;
    double mFirstFramePts = -1.0;
    std::atomic<float> mSpeed{1.0f};
};