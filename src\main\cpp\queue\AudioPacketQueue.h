#ifndef ANDROIDPLAYER_AUDIOPACKETQUEUE_H
#define ANDROIDPLAYER_AUDIOPACKETQUEUE_H

#include <queue>
#include <mutex>
#include <condition_variable>

extern "C" {
#include <libavcodec/avcodec.h>
};

class AudioPacketQueue {
public:
    AudioPacketQueue() = default;
    ~AudioPacketQueue() = default;

    void push(AVPacket *packet) {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_queue.push(packet);
        m_cond.notify_one();
    }

    AVPacket* pop() {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_cond.wait(lock, [this] { return !m_queue.empty() || m_quit; });
        if (m_quit) {
            return nullptr;
        }
        AVPacket *packet = m_queue.front();
        m_queue.pop();
        return packet;
    }

    void clear() {
        std::lock_guard<std::mutex> lock(m_mutex);
        while (!m_queue.empty()) {
            AVPacket *packet = m_queue.front();
            av_packet_free(&packet);
            m_queue.pop();
        }
    }

    void quit() {
        m_quit = true;
        m_cond.notify_all();
    }

    int size() {
        return m_queue.size();
    }

private:
    std::queue<AVPacket*> m_queue;
    std::mutex m_mutex;
    std::condition_variable m_cond;
    bool m_quit = false;
};

#endif //ANDROIDPLAYER_AUDIOPACKETQUEUE_H
