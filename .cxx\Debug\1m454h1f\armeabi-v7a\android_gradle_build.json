{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\.cxx\\Debug\\1m454h1f\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\.cxx\\Debug\\1m454h1f\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"androidplayer::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "androidplayer", "output": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\build\\intermediates\\cxx\\Debug\\1m454h1f\\obj\\armeabi-v7a\\libandroidplayer.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\jniLibs\\armeabi-v7a\\libffmpeg.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}