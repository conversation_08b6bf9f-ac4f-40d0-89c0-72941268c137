#pragma once

extern "C" {
#include <libavcodec/avcodec.h>
}

#include <queue>
#include <mutex>
#include <condition_variable>

class VideoPacketQueue {
public:
    VideoPacketQueue();
    void push(AVPacket* packet);
    AVPacket* pop();         // 阻塞直到有数据
    void clear();
    void abort();
    bool empty();
    size_t size();

private:
    std::queue<AVPacket*> q;
    std::mutex mtx;
    std::condition_variable cond;
    bool mAbort;
};