#ifndef ANDROIDPLAYER_PACKETQUEUE_H
#define ANDROIDPLAYER_PACKETQUEUE_H

#include <queue>
#include <mutex>
#include <condition_variable>

extern "C" {
#include <libavcodec/avcodec.h>
};

class PacketQueue {
public:
    PacketQueue();
    ~PacketQueue();

    void push(AVPacket *packet);
    AVPacket* pop();
    void clear();
    void abort();
    int size();

private:
    std::queue<AVPacket*> mQueue;
    std::mutex mMutex;
    std::condition_variable mCond;
    bool mAbort;
};

#endif //ANDROIDPLAYER_PACKETQUEUE_H
