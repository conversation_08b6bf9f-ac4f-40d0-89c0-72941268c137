#include "YuvDumper.h"
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
}
#include <fstream>

int YuvDumper::decodeToYUV(const std::string& inputPath, const std::string& outputPath) {

    AVFormatContext* fmtCtx = nullptr;
    if (avformat_open_input(&fmtCtx, inputPath.c_str(), nullptr, nullptr) < 0) {
        return -1;
    }

    if (avformat_find_stream_info(fmtCtx, nullptr) < 0) {
        return -1;
    }

    int videoStreamIndex = -1;
    for (int i = 0; i < fmtCtx->nb_streams; ++i) {
        if (fmtCtx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            videoStreamIndex = i;
            break;
        }
    }

    if (videoStreamIndex == -1) return -1;

    AVCodecParameters* codecPar = fmtCtx->streams[videoStreamIndex]->codecpar;
    const AVCodec* codec = avcodec_find_decoder(codecPar->codec_id);
    AVCodecContext* codecCtx = avcodec_alloc_context3(codec);
    avcodec_parameters_to_context(codecCtx, codecPar);
    avcodec_open2(codecCtx, codec, nullptr);

    AVFrame* frame = av_frame_alloc();
    AVPacket* packet = av_packet_alloc();

    SwsContext* swsCtx = sws_getContext(
            codecCtx->width, codecCtx->height, codecCtx->pix_fmt,
            codecCtx->width, codecCtx->height, AV_PIX_FMT_YUV420P,
            SWS_BILINEAR, nullptr, nullptr, nullptr
    );

    std::ofstream out(outputPath, std::ios::binary);
    if (!out.is_open()) return -1;

    AVFrame* yuvFrame = av_frame_alloc();
    int bufferSize = av_image_get_buffer_size(
            AV_PIX_FMT_YUV420P, codecCtx->width, codecCtx->height, 1);
    uint8_t* buffer = (uint8_t*)av_malloc(bufferSize);
    av_image_fill_arrays(yuvFrame->data, yuvFrame->linesize,
                         buffer, AV_PIX_FMT_YUV420P,
                         codecCtx->width, codecCtx->height, 1);

    while (av_read_frame(fmtCtx, packet) >= 0) {
        if (packet->stream_index == videoStreamIndex) {
            avcodec_send_packet(codecCtx, packet);
            while (avcodec_receive_frame(codecCtx, frame) == 0) {
                sws_scale(swsCtx, frame->data, frame->linesize,
                          0, codecCtx->height,
                          yuvFrame->data, yuvFrame->linesize);

                int ySize = codecCtx->width * codecCtx->height;
                int uvSize = ySize / 4;
                out.write((char*)yuvFrame->data[0], ySize);   // Y
                out.write((char*)yuvFrame->data[1], uvSize);  // U
                out.write((char*)yuvFrame->data[2], uvSize);  // V
            }
        }
        av_packet_unref(packet);
    }

    out.close();
    av_free(buffer);
    av_frame_free(&yuvFrame);
    av_frame_free(&frame);
    av_packet_free(&packet);
    avcodec_free_context(&codecCtx);
    avformat_close_input(&fmtCtx);
    sws_freeContext(swsCtx);

    return 0;
}