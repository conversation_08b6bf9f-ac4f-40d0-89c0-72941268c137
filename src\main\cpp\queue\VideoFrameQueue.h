#pragma once
#include <mutex>
#include <condition_variable>
#include <queue>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavutil/mem.h>
}

struct VideoFrame {
    uint8_t* rgba;
    int width;
    int height;
    double pts;

    ~VideoFrame() {
        if (rgba) {
            av_free(rgba);
            rgba = nullptr;
        }
    }
};

class VideoFrameQueue {
public:
    void push(VideoFrame* frame);
    VideoFrame* pop();      // 阻塞
    void clear();
    void abort();
    bool empty();
    size_t size();
private:
    std::queue<VideoFrame*> q;
    std::mutex mtx;
    std::condition_variable cv;
    bool mAbortRequested = false;
};