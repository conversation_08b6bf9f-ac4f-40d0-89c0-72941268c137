{"artifacts": [{"path": "/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/4m444y38/obj/arm64-v8a/libandroidplayer.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 20, "parent": 0}, {"command": 1, "file": 0, "line": 37, "parent": 0}, {"command": 2, "file": 0, "line": 7, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}], "defines": [{"define": "androidplayer_EXPORTS"}], "includes": [{"backtrace": 3, "path": "/home/<USER>/projects/androidplayer/app/src/main/cpp/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "sysroot": {"path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot"}}], "id": "androidplayer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "/home/<USER>/projects/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libffmpeg.so", "role": "libraries"}, {"backtrace": 2, "fragment": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/33/liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "-<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 2, "fragment": "-lGLESv2", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot"}}, "name": "androidplayer", "nameOnDisk": "libandroidplayer.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "AAudioRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ANWRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "native-lib.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/YuvDumper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "queue/VideoPacketQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "demuxer/Demuxer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/VideoDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "render/GLRenderer.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}