cmake_minimum_required(VERSION 3.22.1)

project("androidplayer")

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -fexceptions")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DANDROID_STL=c++_shared")

# 为FFmpeg 7.x添加必要的编译定义
add_definitions(-D__STDC_CONSTANT_MACROS)
add_definitions(-D__STDC_FORMAT_MACROS)
add_definitions(-D__STDC_LIMIT_MACROS)

# 包含头文件目录
include_directories(${CMAKE_SYSROOT}/usr/include)
include_directories(${CMAKE_SOURCE_DIR}/include)

# 查找系统库
find_library(log-lib log)

# 添加 FFmpeg 库作为 IMPORTED 库
# libavutil - FFmpeg核心工具库
add_library(avutil-lib SHARED IMPORTED)
set_target_properties(avutil-lib PROPERTIES
        IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libavutil.so
        IMPORTED_NO_SONAME ON)

# libavcodec - 编解码器库
add_library(avcodec-lib SHARED IMPORTED)
set_target_properties(avcodec-lib PROPERTIES
        IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libavcodec.so
        IMPORTED_NO_SONAME ON)

# libavformat - 格式处理库
add_library(avformat-lib SHARED IMPORTED)
set_target_properties(avformat-lib PROPERTIES
        IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libavformat.so
        IMPORTED_NO_SONAME ON)

# libavdevice - 设备库
add_library(avdevice-lib SHARED IMPORTED)
set_target_properties(avdevice-lib PROPERTIES
        IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libavdevice.so
        IMPORTED_NO_SONAME ON)

# libavfilter - 滤镜库
add_library(avfilter-lib SHARED IMPORTED)
set_target_properties(avfilter-lib PROPERTIES
        IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libavfilter.so
        IMPORTED_NO_SONAME ON)

# libswscale - 图像缩放库
add_library(swscale-lib SHARED IMPORTED)
set_target_properties(swscale-lib PROPERTIES
        IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libswscale.so
        IMPORTED_NO_SONAME ON)

# libswresample - 音频重采样库
add_library(swresample-lib SHARED IMPORTED)
set_target_properties(swresample-lib PROPERTIES
        IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}/libswresample.so
        IMPORTED_NO_SONAME ON)

# 添加你的共享库
add_library(androidplayer SHARED
        AAudioRender.cpp
        ANWRender.cpp
        native-lib.cpp
        decoder/YuvDumper.cpp
        queue/PacketQueue.cpp
        queue/VideoFrameQueue.cpp
        demuxer/Demuxer.cpp
        decoder/VideoDecoder.cpp
        decoder/AudioDecoder.cpp
        render/GLRenderer.cpp
)

# 链接库到你的共享库
target_link_libraries(androidplayer
        ${log-lib}
        android
        aaudio
        EGL
        GLESv2
        # FFmpeg库按依赖顺序链接
        avutil-lib
        swresample-lib
        swscale-lib
        avcodec-lib
        avformat-lib
        avfilter-lib
        avdevice-lib
        z
)
