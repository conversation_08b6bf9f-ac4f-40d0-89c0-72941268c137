[{"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\AAudioRender.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\AAudioRender.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\AAudioRender.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\ANWRender.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\ANWRender.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\ANWRender.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\native-lib.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\native-lib.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\native-lib.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\decoder\\YuvDumper.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\decoder\\YuvDumper.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\decoder\\YuvDumper.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\queue\\PacketQueue.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\queue\\PacketQueue.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\queue\\PacketQueue.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\queue\\VideoFrameQueue.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\queue\\VideoFrameQueue.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\queue\\VideoFrameQueue.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\demuxer\\Demuxer.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\demuxer\\Demuxer.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\demuxer\\Demuxer.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\decoder\\VideoDecoder.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\decoder\\VideoDecoder.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\decoder\\VideoDecoder.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\decoder\\AudioDecoder.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\decoder\\AudioDecoder.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\decoder\\AudioDecoder.cpp"}, {"directory": "C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi30 --gcc-toolchain=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dandroidplayer_EXPORTS -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles\\androidplayer.dir\\render\\GLRenderer.cpp.o -c C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\render\\GLRenderer.cpp", "file": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\render\\GLRenderer.cpp"}]