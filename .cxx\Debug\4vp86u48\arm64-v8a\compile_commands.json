[{"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/AAudioRender.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/AAudioRender.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/AAudioRender.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/ANWRender.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/ANWRender.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/ANWRender.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/native-lib.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/native-lib.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/native-lib.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/YuvDumper.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/YuvDumper.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/queue/VideoPacketQueue.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/queue/VideoPacketQueue.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/queue/VideoPacketQueue.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/queue/VideoFrameQueue.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/queue/VideoFrameQueue.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/queue/VideoFrameQueue.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/demuxer/Demuxer.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/demuxer/Demuxer.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/VideoDecoder.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/VideoDecoder.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android30 --gcc-toolchain=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64 --sysroot=/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC -o CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/render/GLRenderer.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/render/GLRenderer.cpp"}]