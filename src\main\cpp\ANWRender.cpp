/*
 * ANWRender.cpp
 *
 * 基于 ANativeWindow（Surface）的简单视频渲染器实现。
 * 功能：
 *   - 将一帧 RGBA_8888 格式的数据拷贝到 NativeWindow 并显示。
 *   - 支持自定义视频分辨率。
 *
 * 使用步骤：
 *   1. 构造时传入 ANativeWindow*（通常来自 Java Surface）。
 *   2. 调用 init(...) 设置视频宽高与缓冲区格式。
 *   3. 每收到一帧解码后的 RGBA 数据，调用 render(...) 渲染到屏幕。
 *
 * 注意：
 *   - 线程安全由调用者保证。
 *   - 所有错误均返回 -1，成功返回 0。
 */

#include "ANWRender.h"
#include <string.h>
#include "log.h"

#define LOG_TAG "ANWDisplay"

/* ------------------- 构造函数 ------------------- */
/**
 * @param window 与 Java 层 Surface 对应的 ANativeWindow 指针，
 *               生命周期必须长于本对象。
 */
ANWRender::ANWRender(ANativeWindow* window) {
    native_window = window;
}

/* ------------------- 初始化 ------------------- */
/**
 * 设置 NativeWindow 的缓冲区几何属性，使其与视频尺寸匹配。
 *
 * @param videoWidth  视频帧宽度（像素）
 * @param videoHeight 视频帧高度（像素）
 * @return 0 成功，-1 失败（window 为空或设置失败）
 */
int ANWRender::init(int videoWidth, int videoHeight) {
    width  = videoWidth;
    height = videoHeight;

    // 空指针检查
    if (native_window == NULL)
        return -1;

    /*
     * 关键函数：
     * ANativeWindow_setBuffersGeometry()
     * 参数 1：ANativeWindow*
     * 参数 2：期望宽度（0 表示不改变）
     * 参数 3：期望高度（0 表示不改变）
     * 参数 4：像素格式（WINDOW_FORMAT_RGBA_8888 对应 4 字节/像素）
     *
     * 成功返回 0，失败返回负值。
     */
    return ANativeWindow_setBuffersGeometry(native_window,
                                            videoWidth,
                                            videoHeight,
                                            WINDOW_FORMAT_RGBA_8888);
}

/* ------------------- 渲染一帧 ------------------- */
/**
 * 将 RGBA 数据拷贝到 NativeWindow 并提交显示。
 *
 * @param rgba 指向一帧完整 RGBA_8888 数据的指针。
 *             数据大小必须等于 width * height * 4 字节。
 * @return 0 成功，-1 失败（window 为空或数据为空）
 */
int ANWRender::render(uint8_t* rgba) {
    if (native_window == NULL || rgba == NULL)
        return -1;

    // 用于接收锁定的缓冲区信息
    ANativeWindow_Buffer out_buffer;

    /*
     * 1. 锁定当前窗口的缓冲区，获取可写指针。
     *    最后一个参数可传 ANativeWindow_Buffer* 作为 inOutBuffer，这里不需要。
     */
    ANativeWindow_lock(native_window, &out_buffer, NULL);

    /*
     * 2. 计算行大小（字节）：
     *    源数据行大小 = width * 4 (RGBA)
     *    目标缓冲区行大小 = stride * 4，stride 可能 >= width（对齐填充）
     */
    int srcLineSize  = width * 4;
    int dstLineSize  = out_buffer.stride * 4;

    // 获取目标缓冲区首地址
    uint8_t* dstBuffer = static_cast<uint8_t*>(out_buffer.bits);

    /*
     * 3. 逐行拷贝，解决 stride 与 width 不一致的问题。
     *    如果 stride == width，可一次性 memcpy；这里保留通用写法。
     */
    for (int i = 0; i < height; ++i) {
        memcpy(dstBuffer + i * dstLineSize,
               rgba + i * srcLineSize,
               srcLineSize);
    }

    /*
     * 4. 解锁并提交缓冲区到 SurfaceFlinger，使其在屏幕上可见。
     *    若调用 ANativeWindow_unlockAndPost 后再次 lock 失败，
     *    说明窗口已被释放或尺寸改变，需重新 init。
     */
    ANativeWindow_unlockAndPost(native_window);

    return 0;
}