[{"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/AAudioRender.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/AAudioRender.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/AAudioRender.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/ANWRender.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/ANWRender.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/ANWRender.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/native-lib.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/native-lib.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/native-lib.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/YuvDumper.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/YuvDumper.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/queue/VideoPacketQueue.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/queue/VideoPacketQueue.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/queue/VideoPacketQueue.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/demuxer/Demuxer.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/demuxer/Demuxer.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/VideoDecoder.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/VideoDecoder.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -Dandroidplayer_EXPORTS -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -o CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/render/GLRenderer.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/render/GLRenderer.cpp"}, {"directory": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "command": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ --target=aarch64-none-linux-android33 --sysroot=/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/sysroot -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIE -o CMakeFiles/test.dir/test/test.cpp.o -c /home/<USER>/projects/androidplayer/app/src/main/cpp/test/test.cpp", "file": "/home/<USER>/projects/androidplayer/app/src/main/cpp/test/test.cpp"}]