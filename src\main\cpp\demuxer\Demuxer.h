#ifndef ANDROIDPLAYER_DEMUXER_H
#define ANDROIDPLAYER_DEMUXER_H

extern "C" {
#include <libavformat/avformat.h>
}

#include <mutex>
#include <thread>
#include "../queue/PacketQueue.h"

class Demuxer {
public:
    Demuxer();
    ~Demuxer();

    int open(const char* url, PacketQueue* videoQueue, PacketQueue* audioQueue);
    void close();
    void start();
    void stop();

    AVStream* getVideoStream();
    AVStream* getAudioStream();
    AVCodecParameters* getVideoCodecParams();
    AVCodecParameters* getAudioCodecParams();
    double getDuration() const;
    double getCurrentPts();
    int seek(int64_t positionUs);

    int getVideoStreamIndex() const { return mVideoStreamIndex; }
    int getAudioStreamIndex() const { return mAudioStreamIndex; }

private:
    void demux();

    AVFormatContext* mFormatCtx = nullptr;
    int mVideoStreamIndex = -1;
    int mAudioStreamIndex = -1;
    std::mutex mMutex;
    double mDuration = 0.0;
    volatile double mCurrentPts = 0.0;

    PacketQueue* mVideoQueue = nullptr;
    PacketQueue* mAudioQueue = nullptr;
    std::thread* mDemuxThread = nullptr;
    volatile bool mAbort = false;
};

#endif //ANDROIDPLAYER_DEMUXER_H
