                        -H/home/<USER>/projects/androidplayer/app/src/main/cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=33
-DANDROID_PLATFORM=android-33
-D<PERSON>DROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/25.1.8937393
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/usr/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-B/home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a
-GNinja
                        Build command args: []
                        Version: 2