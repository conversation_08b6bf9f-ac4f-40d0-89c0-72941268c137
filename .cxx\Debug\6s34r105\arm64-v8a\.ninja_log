# ninja log v5
1	25	1753252722854118349	/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/arm64-v8a/test	c6c4217300113dc0
2	28	1753251504805275107	CMakeFiles/androidplayer.dir/ANWRender.cpp.o	1b0a9db5e69a72d5
2	58	1753251504834275004	CMakeFiles/androidplayer.dir/native-lib.cpp.o	6651fd84cdc0918a
0	51	1753251504816275068	CMakeFiles/androidplayer.dir/AAudioRender.cpp.o	aec86fec34aad81f
1	54	1753252651423699090	CMakeFiles/test.dir/test/test.cpp.o	68f5b733cb1f7a13
7	678	1753251505452272808	CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o	bace523d1f5c8dcf
51	91	1753251504867274887	CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o	cf6b8222e53cbf74
9	498	1753251505274273441	CMakeFiles/androidplayer.dir/queue/VideoPacketQueue.cpp.o	efa3ec71c6dcd613
12	606	1753251505380273064	CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o	aa6e6ba9552e6148
28	652	1753251505427272897	CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o	59f0bb156ce61d53
0	35	1753252296404487603	/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/arm64-v8a/libandroidplayer.so	33d6b4831cb5b53c
