#include "GLRenderer.h"
#include <android/log.h>
#include <mutex>
#include <chrono>
#include <sys/time.h>

#define LOG_TAG "GLRenderer"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

void checkGlError(const char* op) {
    for (GLint error = glGetError(); error; error = glGetError()) {
        LOGE("after %s() glError (0x%x)\n", op, error);
    }
}

// 获取当前时间（秒）
static double getCurrentTime() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec + tv.tv_usec / 1000000.0;
}

static const char* vertexShaderSrc = R"(
    attribute vec2 aPosition;
    attribute vec2 aTexCoord;
    varying vec2 vTexCoord;
    void main() {
        gl_Position = vec4(aPosition, 0.0, 1.0);
        vTexCoord = aTexCoord;
    }
)";

static const char* fragmentShaderSrc = R"(
    precision mediump float;
    varying vec2 vTexCoord;
    uniform sampler2D uTexture;
    void main() {
        gl_FragColor = texture2D(uTexture, vTexCoord);
    }
)";

GLRenderer::GLRenderer(ANativeWindow* window, VideoFrameQueue* frameQueue) 
    : mWindow(window), mFrameQueue(frameQueue) {}

GLRenderer::~GLRenderer() {
    stop();
}

bool GLRenderer::init(ANativeWindow* window, int width, int height) {
    LOGD("Initializing GLRenderer with window=%p, size=%dx%d", window, width, height);
    
    if (!window) {
        LOGE("Window is null");
        return false;
    }
    
    mWindow = window;
    
    if (!initEGL()) {
        LOGE("Failed to init EGL");
        return false;
    }

    // Get actual surface dimensions
    EGLint surfaceWidth, surfaceHeight;
    eglQuerySurface(mDisplay, mSurface, EGL_WIDTH, &surfaceWidth);
    eglQuerySurface(mDisplay, mSurface, EGL_HEIGHT, &surfaceHeight);
    LOGD("Surface dimensions: %dx%d", surfaceWidth, surfaceHeight);
    
    glViewport(0, 0, surfaceWidth, surfaceHeight);
    checkGlError("glViewport");

    // Compile vertex shader
    GLuint vs = glCreateShader(GL_VERTEX_SHADER);
    glShaderSource(vs, 1, &vertexShaderSrc, nullptr);
    glCompileShader(vs);
    
    GLint compiled;
    glGetShaderiv(vs, GL_COMPILE_STATUS, &compiled);
    if (!compiled) {
        GLint infoLen = 0;
        glGetShaderiv(vs, GL_INFO_LOG_LENGTH, &infoLen);
        if (infoLen > 1) {
            char* infoLog = (char*)malloc(sizeof(char) * infoLen);
            glGetShaderInfoLog(vs, infoLen, nullptr, infoLog);
            LOGE("Error compiling vertex shader: %s", infoLog);
            free(infoLog);
        }
        glDeleteShader(vs);
        return false;
    }
    checkGlError("vertex shader compile");

    // Compile fragment shader
    GLuint fs = glCreateShader(GL_FRAGMENT_SHADER);
    glShaderSource(fs, 1, &fragmentShaderSrc, nullptr);
    glCompileShader(fs);
    
    glGetShaderiv(fs, GL_COMPILE_STATUS, &compiled);
    if (!compiled) {
        GLint infoLen = 0;
        glGetShaderiv(fs, GL_INFO_LOG_LENGTH, &infoLen);
        if (infoLen > 1) {
            char* infoLog = (char*)malloc(sizeof(char) * infoLen);
            glGetShaderInfoLog(fs, infoLen, nullptr, infoLog);
            LOGE("Error compiling fragment shader: %s", infoLog);
            free(infoLog);
        }
        glDeleteShader(vs);
        glDeleteShader(fs);
        return false;
    }
    checkGlError("fragment shader compile");

    // Create and link program
    mProgram = glCreateProgram();
    glAttachShader(mProgram, vs);
    glAttachShader(mProgram, fs);
    glLinkProgram(mProgram);
    
    GLint linked;
    glGetProgramiv(mProgram, GL_LINK_STATUS, &linked);
    if (!linked) {
        GLint infoLen = 0;
        glGetProgramiv(mProgram, GL_INFO_LOG_LENGTH, &infoLen);
        if (infoLen > 1) {
            char* infoLog = (char*)malloc(sizeof(char) * infoLen);
            glGetProgramInfoLog(mProgram, infoLen, nullptr, infoLog);
            LOGE("Error linking program: %s", infoLog);
            free(infoLog);
        }
        glDeleteProgram(mProgram);
        glDeleteShader(vs);
        glDeleteShader(fs);
        return false;
    }
    
    glDeleteShader(vs);
    glDeleteShader(fs);
    checkGlError("program link");

    glUseProgram(mProgram);
    checkGlError("glUseProgram");

    // Setup vertex attributes
    static const float vertices[] = {
        -1.0f, -1.0f, 0.0f, 1.0f, // Bottom-left
         1.0f, -1.0f, 1.0f, 1.0f, // Bottom-right
        -1.0f,  1.0f, 0.0f, 0.0f, // Top-left
         1.0f,  1.0f, 1.0f, 0.0f  // Top-right
    };
    
    GLint posLoc = glGetAttribLocation(mProgram, "aPosition");
    GLint texCoordLoc = glGetAttribLocation(mProgram, "aTexCoord");
    
    if (posLoc == -1 || texCoordLoc == -1) {
        LOGE("Failed to get attribute locations: pos=%d, tex=%d", posLoc, texCoordLoc);
        return false;
    }
    
    glVertexAttribPointer(posLoc, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), vertices);
    glEnableVertexAttribArray(posLoc);
    glVertexAttribPointer(texCoordLoc, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), vertices + 2);
    glEnableVertexAttribArray(texCoordLoc);
    checkGlError("vertex attributes");

    // Create texture
    glGenTextures(1, &mTexture);
    glBindTexture(GL_TEXTURE_2D, mTexture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    
    GLint texLoc = glGetUniformLocation(mProgram, "uTexture");
    glUniform1i(texLoc, 0);
    checkGlError("texture setup");

    LOGD("GLRenderer initialization successful");
    return true;
}

void GLRenderer::updateWindow(ANativeWindow* window) {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mWindow != window) {
        LOGD("Updating window from %p to %p", mWindow, window);
        releaseEGL();
        mWindow = window;
        if (mWindow) {
            initEGL();
        }
    }
}

void GLRenderer::renderFrame(const uint8_t* rgba, int width, int height) {
    if (!rgba || width <= 0 || height <= 0) {
        LOGE("Invalid frame data: rgba=%p, size=%dx%d", rgba, width, height);
        return;
    }
    
    std::lock_guard<std::mutex> lock(mMutex);
    
    if (mDisplay == EGL_NO_DISPLAY || mSurface == EGL_NO_SURFACE) {
        LOGE("EGL not properly initialized");
        return;
    }
    
    glClearColor(0.0f, 0.0f, 0.0f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT);
    checkGlError("glClear");
    
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, mTexture);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, rgba);
    checkGlError("glTexImage2D");
    
    glDrawArrays(GL_TRIANGLE_STRIP, 0, 4);
    checkGlError("glDrawArrays");
    
    EGLBoolean result = eglSwapBuffers(mDisplay, mSurface);
    if (result != EGL_TRUE) {
        LOGE("eglSwapBuffers failed: 0x%x", eglGetError());
    }
}

void GLRenderer::release() {
    std::lock_guard<std::mutex> lock(mMutex);
    LOGD("Releasing GLRenderer resources");
    
    if (mTexture != 0) {
        glDeleteTextures(1, &mTexture);
        mTexture = 0;
    }
    
    if (mProgram != 0) {
        glDeleteProgram(mProgram);
        mProgram = 0;
    }
    
    releaseEGL();
}

void GLRenderer::start() {
    LOGD("Starting GLRenderer");
    mRunning = true;
    mRenderThread = new std::thread(&GLRenderer::renderLoop, this);
}

void GLRenderer::stop() {
    LOGD("Stopping GLRenderer");
    mRunning = false;
    
    if (mFrameQueue) {
        mFrameQueue->abort();
    }
    
    if (mRenderThread && mRenderThread->joinable()) {
        mRenderThread->join();
        delete mRenderThread;
        mRenderThread = nullptr;
    }
    
    release();
}

void GLRenderer::renderLoop() {
    if (!init(mWindow, 0, 0)) {
        return;
    }

    mStartTime = getCurrentTime();
    mFirstFramePts = -1.0;

    while (mRunning) {
        VideoFrame* frame = mFrameQueue->pop();
        if (!frame) {
            break;
        }

        // 再次检查mRunning，防止在pop()期间停止
        if (!mRunning) {
            delete frame;
            break;
        }

        if (frame->rgba && frame->width > 0 && frame->height > 0) {
            // 时间控制：根据帧的PTS控制播放速度
            if (mFirstFramePts < 0) {
                mFirstFramePts = frame->pts;
            }

            double currentTime = getCurrentTime();
            double elapsedTime = currentTime - mStartTime;
            float currentSpeed = mSpeed.load();
            double framePts = (frame->pts - mFirstFramePts) / currentSpeed; // 根据播放速度调整时间戳

            // 如果当前帧的时间戳大于已播放时间，则等待
            if (framePts > elapsedTime) {
                double sleepTime = framePts - elapsedTime;
                if (sleepTime > 0 && sleepTime < 1.0) { // 最多等待1秒
                    std::this_thread::sleep_for(std::chrono::microseconds((int)(sleepTime * 1000000)));
                }
            }

            renderFrame(frame->rgba, frame->width, frame->height);
        }

        // VideoFrame的析构函数会自动释放rgba内存
        delete frame;
    }
    
    LOGD("Render loop ended");
    release();
}

void GLRenderer::resetTiming() {
    mStartTime = getCurrentTime();
    mFirstFramePts = -1.0;
}

void GLRenderer::setSpeed(float speed) {
    if (speed <= 0) return;
    mSpeed.store(speed);
    LOGD("GLRenderer speed set to: %f", speed);
}

bool GLRenderer::initEGL() {
    LOGD("Initializing EGL");
    
    if (!mWindow) {
        LOGE("Native window is null");
        return false;
    }
    
    mDisplay = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    if (mDisplay == EGL_NO_DISPLAY) {
        LOGE("eglGetDisplay failed: 0x%x", eglGetError());
        return false;
    }

    EGLint major, minor;
    if (!eglInitialize(mDisplay, &major, &minor)) {
        LOGE("eglInitialize failed: 0x%x", eglGetError());
        return false;
    }
    LOGD("EGL version: %d.%d", major, minor);

    const EGLint configAttribs[] = {
        EGL_SURFACE_TYPE, EGL_WINDOW_BIT,
        EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT,
        EGL_RED_SIZE, 8,
        EGL_GREEN_SIZE, 8,
        EGL_BLUE_SIZE, 8,
        EGL_ALPHA_SIZE, 8,
        EGL_DEPTH_SIZE, 0,
        EGL_STENCIL_SIZE, 0,
        EGL_NONE
    };

    EGLConfig config;
    EGLint numConfigs;
    if (!eglChooseConfig(mDisplay, configAttribs, &config, 1, &numConfigs) || numConfigs == 0) {
        LOGE("eglChooseConfig failed: 0x%x", eglGetError());
        return false;
    }

    const EGLint contextAttribs[] = {
        EGL_CONTEXT_CLIENT_VERSION, 2,
        EGL_NONE
    };

    mContext = eglCreateContext(mDisplay, config, EGL_NO_CONTEXT, contextAttribs);
    if (mContext == EGL_NO_CONTEXT) {
        LOGE("eglCreateContext failed: 0x%x", eglGetError());
        return false;
    }

    mSurface = eglCreateWindowSurface(mDisplay, config, mWindow, nullptr);
    if (mSurface == EGL_NO_SURFACE) {
        LOGE("eglCreateWindowSurface failed: 0x%x", eglGetError());
        return false;
    }

    if (!eglMakeCurrent(mDisplay, mSurface, mSurface, mContext)) {
        LOGE("eglMakeCurrent failed: 0x%x", eglGetError());
        return false;
    }

    LOGD("EGL initialization successful");
    return true;
}

void GLRenderer::releaseEGL() {
    LOGD("Releasing EGL resources");
    
    if (mDisplay != EGL_NO_DISPLAY) {
        eglMakeCurrent(mDisplay, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
        
        if (mSurface != EGL_NO_SURFACE) {
            eglDestroySurface(mDisplay, mSurface);
            mSurface = EGL_NO_SURFACE;
        }
        
        if (mContext != EGL_NO_CONTEXT) {
            eglDestroyContext(mDisplay, mContext);
            mContext = EGL_NO_CONTEXT;
        }
        
        eglTerminate(mDisplay);
        mDisplay = EGL_NO_DISPLAY;
    }
}