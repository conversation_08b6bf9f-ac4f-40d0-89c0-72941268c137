# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/Android/Sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake"
  "/home/<USER>/Android/Sdk/ndk/21.3.6528147/build/cmake/platforms.cmake"
  "CMakeFiles/3.22.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.22.1/CMakeSystem.cmake"
  "/home/<USER>/projects/androidplayer/app/src/main/cpp/test/CMakeLists.txt"
  "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Clang-C.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/Clang.cmake"
  "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Android.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/test.dir/DependInfo.cmake"
  )
