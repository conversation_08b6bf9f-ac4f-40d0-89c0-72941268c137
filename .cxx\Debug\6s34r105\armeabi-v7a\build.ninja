# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: androidplayer
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target androidplayer


#############################################
# Order-only phony target for androidplayer

build cmake_object_order_depends_target_androidplayer: phony || CMakeFiles/androidplayer.dir

build CMakeFiles/androidplayer.dir/AAudioRender.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/AAudioRender.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/AAudioRender.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb

build CMakeFiles/androidplayer.dir/ANWRender.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/ANWRender.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/ANWRender.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb

build CMakeFiles/androidplayer.dir/native-lib.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/native-lib.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/native-lib.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb

build CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/YuvDumper.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir/decoder
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb

build CMakeFiles/androidplayer.dir/queue/VideoPacketQueue.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/queue/VideoPacketQueue.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/queue/VideoPacketQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir/queue
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb

build CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/demuxer/Demuxer.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir/demuxer
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb

build CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/decoder/VideoDecoder.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir/decoder
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb

build CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o: CXX_COMPILER__androidplayer_Debug /home/<USER>/projects/androidplayer/app/src/main/cpp/render/GLRenderer.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -I/home/<USER>/projects/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles/androidplayer.dir/render
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target androidplayer


#############################################
# Link the shared library /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.so

build /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.so: CXX_SHARED_LIBRARY_LINKER__androidplayer_Debug CMakeFiles/androidplayer.dir/AAudioRender.cpp.o CMakeFiles/androidplayer.dir/ANWRender.cpp.o CMakeFiles/androidplayer.dir/native-lib.cpp.o CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o CMakeFiles/androidplayer.dir/queue/VideoPacketQueue.cpp.o CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o | /home/<USER>/projects/androidplayer/app/src/main/cpp/../jniLibs/armeabi-v7a/libffmpeg.so /home/<USER>/projects/androidplayer/app/src/main/cpp/../jniLibs/armeabi-v7a/libffmpeg.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -static-libstdc++ -Wl,--build-id=sha1 -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -landroid  /home/<USER>/projects/androidplayer/app/src/main/cpp/../jniLibs/armeabi-v7a/libffmpeg.so  -llog  -laaudio  -lEGL  -lGLESv2  /home/<USER>/projects/androidplayer/app/src/main/cpp/../jniLibs/armeabi-v7a/libffmpeg.so  -latomic -lm
  OBJECT_DIR = CMakeFiles/androidplayer.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libandroidplayer.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles/androidplayer.dir/
  TARGET_FILE = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.so
  TARGET_PDB = /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/projects/androidplayer/app/src/main/cpp -B/home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build androidplayer: phony /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.so

build libandroidplayer.so: phony /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/projects/androidplayer/app/.cxx/Debug/6s34r105/armeabi-v7a

build all: phony /home/<USER>/projects/androidplayer/app/build/intermediates/cxx/Debug/6s34r105/obj/armeabi-v7a/libandroidplayer.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android-legacy.toolchain.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/flags.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Clang.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Determine.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Initialize.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Determine-Compiler.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/platforms.cmake /home/<USER>/projects/androidplayer/app/src/main/cpp/CMakeLists.txt /usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.22/Modules/CMakeCInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang.cmake /usr/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /usr/share/cmake-3.22/Modules/Platform/Android.cmake /usr/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.22.1/CMakeCCompiler.cmake CMakeFiles/3.22.1/CMakeCXXCompiler.cmake CMakeFiles/3.22.1/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android-legacy.toolchain.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/flags.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Clang.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Determine.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Initialize.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Determine-Compiler.cmake /home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/platforms.cmake /home/<USER>/projects/androidplayer/app/src/main/cpp/CMakeLists.txt /usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.22/Modules/CMakeCInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang.cmake /usr/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /usr/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /usr/share/cmake-3.22/Modules/Platform/Android.cmake /usr/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.22.1/CMakeCCompiler.cmake CMakeFiles/3.22.1/CMakeCXXCompiler.cmake CMakeFiles/3.22.1/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
