{"artifacts": [{"path": "/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/2v3w3q4b/obj/arm64-v8a/libandroidplayer.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 20, "parent": 0}, {"command": 1, "file": 0, "line": 33, "parent": 0}, {"command": 1, "file": 0, "line": 43, "parent": 0}, {"command": 2, "file": 0, "line": 7, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}], "defines": [{"define": "androidplayer_EXPORTS"}], "includes": [{"backtrace": 4, "path": "/home/<USER>/projects/androidplayer/app/src/main/cpp/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8], "sysroot": {"path": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot"}}], "id": "androidplayer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libgcc_real.a -Wl,--exclude-libs,libatomic.a -static-libstdc++ -Wl,--build-id -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "-<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 2, "fragment": "-lGLESv2", "role": "libraries"}, {"backtrace": 2, "fragment": "/home/<USER>/projects/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libffmpeg.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/30/liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 2, "fragment": "-lGLESv2", "role": "libraries"}, {"backtrace": 2, "fragment": "-<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/30/liblog.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/sysroot"}}, "name": "androidplayer", "nameOnDisk": "libandroidplayer.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "AAudioRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ANWRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "native-lib.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/YuvDumper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "queue/VideoPacketQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "queue/VideoFrameQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "demuxer/Demuxer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/VideoDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "render/GLRenderer.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}