#include <jni.h>
#include <android/native_window_jni.h>
#include <android/native_activity.h>
#include <android/log.h>
#include <atomic>
#include <thread>
#include <chrono>
#include <mutex>
#include <condition_variable>

// 日志宏
#define LOG_TAG "PlayerJNI"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 本地头文件
#include "render/GLRenderer.h"
#include "decoder/VideoDecoder.h"
#include "queue/PacketQueue.h"
#include "queue/VideoFrameQueue.h"
#include "demuxer/Demuxer.h"
#include "decoder/AudioDecoder.h"
#include "utils/RingBuffer.h"
#include "AAudioRender.h"

extern "C" {
#include <libavformat/avformat.h>
}

// 全局变量
static ANativeWindow* gWindow = nullptr;
static GLRenderer* gRenderer = nullptr;
static VideoDecoder* gDecoder = nullptr;
static PacketQueue* gVideoPacketQueue = nullptr;
static VideoFrameQueue* gFrameQueue = nullptr;
static Demuxer* gDemuxer = nullptr;
static AudioDecoder* gAudioDecoder = nullptr;
static PacketQueue* gAudioPacketQueue = nullptr;
static RingBuffer* gRingBuffer = nullptr;
static AAudioRender* gAudioRender = nullptr;

static jobject gSurface = nullptr; // 保存 surface 的全局引用

static volatile bool gIsPlaying = false;
static volatile bool gIsPaused = false;

// 声明一个函数指针，用于seek后回调通知Java层
static jmethodID gOnSeekComplete = nullptr;
static JavaVM* gJvm = nullptr;

// 新增互斥锁和条件变量用于线程同步
static std::mutex gStateMutex;
static std::condition_variable gStateCond;

// 新增seek相关变量
static std::atomic<bool> gSeekRequested{false};
static std::atomic<double> gSeekPosition{0.0};

// 播放速度跟踪变量
static std::atomic<float> gCurrentSpeed{1.0f};
static std::atomic<double> gPlaybackStartTime{0.0};
static std::atomic<double> gPlaybackStartPts{0.0};

// 获取当前时间（秒）
static double getCurrentTimeSeconds() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

// AAudio callback function
aaudio_data_callback_result_t audioCallback(AAudioStream *stream, void *userData, void *audioData, int32_t numFrames) {
    auto* renderer = (AAudioRender*)userData;
    if (renderer && gRingBuffer) {
        int bytesPerFrame = renderer->getChannelCount() * (renderer->getFormat() == AAUDIO_FORMAT_PCM_I16 ? 2 : 4);
        int bytesToRead = numFrames * bytesPerFrame;
        gRingBuffer->read(static_cast<uint8_t *>(audioData), bytesToRead);
    }
    return AAUDIO_CALLBACK_RESULT_CONTINUE;
}

extern "C" {

// JNI OnLoad
JNIEXPORT jint
JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    gJvm = vm;
    // av_register_all() 在FFmpeg 4.0+版本中已被弃用，不再需要调用
    // 现代FFmpeg会自动注册所有编解码器和复用器
    return JNI_VERSION_1_6;
}

// JNI nativeStop
JNIEXPORT jint JNICALL
Java_com_example_androidplayer_Player_nativeStop(JNIEnv *env, jobject thiz) {
    LOGD("Stopping player");
    
    // 设置停止标志
    gIsPlaying = false;
    gIsPaused = false;
    
    // 唤醒所有等待的线程
    {
        std::lock_guard<std::mutex> lock(gStateMutex);
        gStateCond.notify_all();
    }
    
    // 停止队列，唤醒阻塞的线程
    if (gVideoPacketQueue) {
        gVideoPacketQueue->abort();
    }
    if (gFrameQueue) {
        gFrameQueue->abort();
    }
    if (gAudioPacketQueue) {
        gAudioPacketQueue->abort();
    }

    // 停止并删除组件，它们的析构函数会处理线程和内部资源
    if (gDemuxer) {
        delete gDemuxer; // Destructor calls close(), which calls stop()
        gDemuxer = nullptr;
    }

    if (gDecoder) {
        delete gDecoder; // Destructor calls stop()
        gDecoder = nullptr;
    }

    if (gAudioDecoder) {
        delete gAudioDecoder; // Destructor calls stop()
        gAudioDecoder = nullptr;
    }

    // 先停止渲染器，确保它不再访问VideoFrame
    if (gRenderer) {
        gRenderer->stop();
        delete gRenderer;
        gRenderer = nullptr;
    }

    if (gAudioRender) {
        delete gAudioRender;
        gAudioRender = nullptr;
    }

    // 清理队列（渲染器已停止，安全清理）
    if (gVideoPacketQueue) {
        delete gVideoPacketQueue;
        gVideoPacketQueue = nullptr;
    }

    if (gFrameQueue) {
        gFrameQueue->clear();
        delete gFrameQueue;
        gFrameQueue = nullptr;
    }

    if (gAudioPacketQueue) {
        delete gAudioPacketQueue;
        gAudioPacketQueue = nullptr;
    }

    if (gRingBuffer) {
        delete gRingBuffer;
        gRingBuffer = nullptr;
    }

    // 释放窗口
    if (gWindow) {
        ANativeWindow_release(gWindow);
        gWindow = nullptr;
    }

    // 释放Surface全局引用
    if (gSurface) {
        env->DeleteGlobalRef(gSurface);
        gSurface = nullptr;
    }
    
    LOGD("Player stopped successfully");
    return 0;
}

// JNI nativePlay
JNIEXPORT jint JNICALL
Java_com_example_androidplayer_Player_nativePlay(JNIEnv *env, jobject thiz, jstring path, jobject surface) {
    LOGD("Starting to play video");
    
    if (gIsPlaying) {
        LOGD("Already playing, stopping first");
        Java_com_example_androidplayer_Player_nativeStop(env, thiz);
    }

    int ret = 0;
    const char* video_path = nullptr;
    const char* final_video_path = nullptr;

    // 1. 检查surface参数
    if (surface == nullptr) {
        LOGE("Surface parameter is null");
        ret = -1;
        goto end;
    }

    // 为 surface 创建全局引用
    gSurface = env->NewGlobalRef(surface);
    if (gSurface == nullptr) {
        LOGE("Failed to create global ref for surface");
        ret = -1;
        goto end;
    }

    // 2. 从 surface 获取 ANativeWindow
    gWindow = ANativeWindow_fromSurface(env, gSurface);
    if (!gWindow) {
        LOGE("Failed to get window from surface");
        ret = -1;
        goto cleanup_surface;
    }
    
    LOGD("Native window created: %p", gWindow);

    // 3. 创建队列
    gVideoPacketQueue = new PacketQueue();
    gFrameQueue  = new VideoFrameQueue();
    gAudioPacketQueue = new PacketQueue();
    gRingBuffer = new RingBuffer(1024 * 1024); // 1MB buffer

    if (!gVideoPacketQueue || !gFrameQueue || !gAudioPacketQueue || !gRingBuffer) {
        LOGE("Failed to create queues or ring buffer");
        ret = -1;
        goto cleanup_queues;
    }

    // 4. 获取文件路径
    video_path = env->GetStringUTFChars(path, nullptr);
    if (!video_path) {
        LOGE("Failed to get video path");
        ret = -1;
        goto cleanup_queues;
    }
    
    LOGD("Video path from Java: %s", video_path);

    final_video_path = video_path;
    if (strncmp(video_path, "file://", 7) == 0) {
        final_video_path += 7;
    }

    // 5. 初始化解复用器
    gDemuxer = new Demuxer();
    ret = gDemuxer->open(final_video_path, gVideoPacketQueue, gAudioPacketQueue);
    if (ret < 0) {
        LOGE("Failed to open demuxer, ret=%d", ret);
        goto cleanup_demuxer;
    }
    
    LOGD("Demuxer opened successfully");

    // 6. 初始化音视频解码器
    // Video
    if (gDemuxer->getVideoStream()) {
        AVStream* videoStream = gDemuxer->getVideoStream();
        LOGD("native-lib: Got video stream %p from demuxer. Codec ID: %d (%s)", 
             videoStream, 
             videoStream ? videoStream->codecpar->codec_id : -1,
             videoStream ? avcodec_get_name(videoStream->codecpar->codec_id) : "N/A");

        gDecoder = new VideoDecoder();
        if (gDecoder->open(videoStream, gVideoPacketQueue, gFrameQueue) != 0) {
            LOGE("Failed to open video decoder");
            goto cleanup_decoder;
        }
        LOGD("Video Decoder opened successfully");
    }

    // Audio
    if (gDemuxer->getAudioStream()) {
        gAudioDecoder = new AudioDecoder();
        if (!gAudioDecoder->open(gDemuxer->getAudioStream(), gAudioPacketQueue, gRingBuffer)) {
            LOGE("Failed to open audio decoder");
            goto cleanup_decoder;
        }
        LOGD("Audio Decoder opened successfully");
    }

    // 7. 创建并启动渲染器
    // Video
    gRenderer = new GLRenderer(gWindow, gFrameQueue);
    if (!gRenderer) {
        LOGE("Failed to create video renderer");
        ret = -1;
        goto cleanup_decoder;
    }
    gRenderer->start();
    LOGD("Video Renderer started");

    // Audio
    if (gAudioDecoder) {
        gAudioRender = new AAudioRender();
        gAudioRender->configure(gAudioDecoder->getSampleRate(), gAudioDecoder->getChannels(), AAUDIO_FORMAT_PCM_I16);
        gAudioRender->setCallback(audioCallback, gAudioRender);
        gAudioRender->start();
        LOGD("Audio Renderer started");
    }

    // 8. 设置播放状态
    gIsPlaying = true;
    gIsPaused = false;

    // 初始化播放速度
    gCurrentSpeed.store(1.0f);
    LOGD("Playback started with normal speed");

    // 9. 启动解复用和解码线程
    if (gDecoder) {
        gDecoder->start();
    }
    if (gAudioDecoder) {
        gAudioDecoder->start();
    }
    gDemuxer->start();

    env->ReleaseStringUTFChars(path, video_path);
    LOGD("Video playback started successfully");
    return 0;

    // 清理逻辑
cleanup_decoder:
    if (gDecoder) {
        delete gDecoder;
        gDecoder = nullptr;
    }
    if (gAudioDecoder) {
        delete gAudioDecoder;
        gAudioDecoder = nullptr;
    }

cleanup_demuxer:
    if (gDemuxer) {
        gDemuxer->close();
        delete gDemuxer;
        gDemuxer = nullptr;
    }

cleanup_queues:
    if (gVideoPacketQueue) {
        delete gVideoPacketQueue;
        gVideoPacketQueue = nullptr;
    }
    if (gFrameQueue) {
        delete gFrameQueue;
        gFrameQueue = nullptr;
    }
    if (gAudioPacketQueue) {
        delete gAudioPacketQueue;
        gAudioPacketQueue = nullptr;
    }
    if (gRingBuffer) {
        delete gRingBuffer;
        gRingBuffer = nullptr;
    }

cleanup_window:
    if (gWindow) {
        ANativeWindow_release(gWindow);
        gWindow = nullptr;
    }

cleanup_surface:
    if (gSurface) {
        env->DeleteGlobalRef(gSurface);
        gSurface = nullptr;
    }

end:
    if (video_path) {
        env->ReleaseStringUTFChars(path, video_path);
    }
    gIsPlaying = false;
    return ret;
}

// JNI nativePause
JNIEXPORT void JNICALL
Java_com_example_androidplayer_Player_nativePause(JNIEnv *env, jobject thiz, jboolean pause) {
    LOGD("Pause requested: %s", pause ? "true" : "false");
    
    std::lock_guard<std::mutex> lock(gStateMutex);
    gIsPaused = pause;
    
    if (!pause) {
        // 恢复播放，唤醒等待的线程
        gStateCond.notify_all();
        // 重置渲染器的时间控制
        if (gRenderer) {
            gRenderer->resetTiming();
        }
        LOGD("Playback resumed");
    } else {
        LOGD("Playback paused");
    }
}

// JNI nativeSeek
JNIEXPORT jint JNICALL
Java_com_example_androidplayer_Player_nativeSeek(JNIEnv *env, jobject thiz, jdouble position) {
    LOGD("Seek requested to position: %f", position);
    
    if (!gIsPlaying || !gDemuxer) {
        LOGE("Cannot seek: not playing or demuxer not available");
        return -1;
    }
    
    // 设置seek标志和位置
    gSeekPosition = position;
    gSeekRequested = true;
    
    // 清空队列
    if (gVideoPacketQueue) {
        gVideoPacketQueue->clear();
    }
    if (gFrameQueue) {
        gFrameQueue->clear();
    }
    if (gAudioPacketQueue) {
        gAudioPacketQueue->clear();
    }
    if (gRingBuffer) {
        gRingBuffer->flush();
    }

    // 执行seek操作
    double duration = gDemuxer->getDuration();
    int64_t seek_target = (int64_t)(position * duration * AV_TIME_BASE);
    
    gDemuxer->seek(seek_target);

    // 刷新解码器
    if (gDecoder) {
        gDecoder->flush();
    }
    if (gAudioDecoder) {
        gAudioDecoder->flush();
    }

    // 重置渲染器的时间控制
    if (gRenderer) {
        gRenderer->resetTiming();
    }

    LOGD("Seek completed to position: %f", position);
    gSeekRequested = false;
    return 0;
}

// JNI nativeGetDuration
JNIEXPORT jdouble JNICALL
Java_com_example_androidplayer_Player_nativeGetDuration(JNIEnv *env, jobject thiz) {
    if (gDemuxer) {
        return gDemuxer->getDuration();
    }
    return 0;
}

// JNI nativeGetPosition
JNIEXPORT jdouble JNICALL
Java_com_example_androidplayer_Player_nativeGetPosition(JNIEnv *env, jobject thiz) {
    if (!gDemuxer) {
        return 0.0;
    }

    // 简化：直接返回demuxer的当前位置
    // GLRenderer会根据播放速度控制实际的播放时机
    return gDemuxer->getCurrentPts();
}

// JNI nativeSetSpeed
JNIEXPORT jint JNICALL
Java_com_example_androidplayer_Player_nativeSetSpeed(JNIEnv *env, jobject thiz, jfloat speed) {
    LOGD("Setting playback speed to: %f", speed);

    if (speed <= 0.0f || speed > 10.0f) {
        LOGE("Invalid speed value: %f", speed);
        return -1;
    }

    // 更新全局速度
    gCurrentSpeed.store(speed);

    // 设置视频解码器速度
    if (gDecoder) {
        gDecoder->setSpeed(speed);
        LOGD("Video decoder speed set to %f", speed);
    }

    // 设置音频解码器速度（目前不实际使用，只记录）
    if (gAudioDecoder) {
        gAudioDecoder->setSpeed(speed);
        LOGD("Audio decoder speed set to %f", speed);
    }

    // 设置渲染器速度（主要的速度控制）
    if (gRenderer) {
        gRenderer->setSpeed(speed);
        gRenderer->resetTiming(); // 重置时间控制
        LOGD("Renderer speed set to %f and timing reset", speed);
    }

    return 0;
}

// JNI nativeTogglePlayPause
JNIEXPORT void JNICALL
Java_com_example_androidplayer_Player_nativeTogglePlayPause(JNIEnv *env, jobject thiz) {
    if (!gDecoder) {
        return;
    }
    if (gIsPlaying) {
        LOGD("Pausing video");
        gDecoder->pause();
    } else {
        LOGD("Resuming video");
        gDecoder->resume();
    }
    gIsPlaying = !gIsPlaying;
}

} // extern "C"