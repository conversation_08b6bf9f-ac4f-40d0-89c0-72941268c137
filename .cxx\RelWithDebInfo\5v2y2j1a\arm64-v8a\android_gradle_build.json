{"buildFiles": ["D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\APP\\Android-studio\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\.cxx\\RelWithDebInfo\\5v2y2j1a\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\APP\\Android-studio\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\.cxx\\RelWithDebInfo\\5v2y2j1a\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"androidplayer::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "androidplayer", "output": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5v2y2j1a\\obj\\arm64-v8a\\libandroidplayer.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}