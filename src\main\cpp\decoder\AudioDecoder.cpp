#include "AudioDecoder.h"
#include <android/log.h>

// Updated to use FFmpeg 6.x+ new channel layout API

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavutil/channel_layout.h>
#include <libavutil/opt.h>
#include <libavutil/error.h>
#include <libswresample/swresample.h>
}

#define LOG_TAG "AudioDecoder"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define SAMPLES_ONE_FRAME 1024

// 简化错误处理，避免lambda表达式
static const char* get_error_string(int errnum) {
    static char error_buf[256];
    av_strerror(errnum, error_buf, sizeof(error_buf));
    return error_buf;
}

// 修正音频编解码器ID
static AVCodecID fixAudioCodecId(AVCodecID codecId) {
    if (codecId == -1275068297) {
        LOGD("Detected problematic audio codec_id, assuming AAC");
        return AV_CODEC_ID_AAC;
    }

    if (codecId < 0 || codecId > 100000) {
        LOGD("Invalid audio codec_id %d, assuming AAC", codecId);
        return AV_CODEC_ID_AAC;
    }

    return codecId;
}

AudioDecoder::AudioDecoder() = default;

AudioDecoder::~AudioDecoder() {
    stop();
}

bool AudioDecoder::open(AVStream *audio_stream, PacketQueue* packetQueue, RingBuffer *ring_buffer) {
    m_packet_queue = packetQueue;
    m_ring_buffer = ring_buffer;

    // 添加音频编解码器调试信息
    LOGD("AudioDecoder::open() codec_id: %d (%s)",
         audio_stream->codecpar->codec_id,
         avcodec_get_name(audio_stream->codecpar->codec_id));
    LOGD("Audio sample_rate: %d, channels: %d",
         audio_stream->codecpar->sample_rate,
         audio_stream->codecpar->ch_layout.nb_channels);

    // 修正编解码器ID
    AVCodecID correctedCodecId = fixAudioCodecId(audio_stream->codecpar->codec_id);
    bool needsCorrection = (correctedCodecId != audio_stream->codecpar->codec_id);

    const AVCodec *codec = avcodec_find_decoder(correctedCodecId);
    if (!codec) {
        LOGE("Failed to find audio codec for codec_id: %d", correctedCodecId);
        return false;
    }
    LOGD("Found audio decoder: %s", codec->name);

    m_codec_ctx = avcodec_alloc_context3(codec);
    if (!m_codec_ctx) {
        LOGE("Failed to allocate codec context");
        return false;
    }

    if (avcodec_parameters_to_context(m_codec_ctx, audio_stream->codecpar) < 0) {
        LOGE("Failed to copy codec parameters to context");
        return false;
    }

    // 如果需要修正，手动设置正确的参数
    if (needsCorrection) {
        m_codec_ctx->codec_id = correctedCodecId;
        m_codec_ctx->sample_rate = 44100;  // 设置默认采样率
        // 使用新的 ch_layout API 替代已弃用的 channels 和 channel_layout
        av_channel_layout_default(&m_codec_ctx->ch_layout, 2);
        m_codec_ctx->sample_fmt = AV_SAMPLE_FMT_FLTP; // AAC默认格式
        m_codec_ctx->codec_type = AVMEDIA_TYPE_AUDIO;
    }

    if (avcodec_open2(m_codec_ctx, codec, nullptr) < 0) {
        LOGE("Failed to open audio codec");
        return false;
    }

    m_frame = av_frame_alloc();
    if (!m_frame) {
        LOGE("Failed to allocate frame");
        return false;
    }

    // ======= 重采样参数 =======
    m_out_channels      = 2;
    m_out_sample_rate   = 44100;
    m_out_sample_fmt    = AV_SAMPLE_FMT_S16;

    /* ---------- FFmpeg 7.x 新 API ---------- */
    // 使用新的 channel layout API
    AVChannelLayout in_ch_layout, out_ch_layout;

    // 设置输入通道布局 - 使用实际的输入通道布局
    if (audio_stream->codecpar->ch_layout.nb_channels > 0) {
        av_channel_layout_copy(&in_ch_layout, &audio_stream->codecpar->ch_layout);
    } else {
        // 如果没有通道布局信息，根据通道数设置默认布局
        if (audio_stream->codecpar->ch_layout.nb_channels == 1) {
            // 创建临时变量来避免对临时对象取地址
            AVChannelLayout mono_layout = AV_CHANNEL_LAYOUT_MONO;
            av_channel_layout_copy(&in_ch_layout, &mono_layout);
        } else {
            // 创建临时变量来避免对临时对象取地址
            AVChannelLayout stereo_layout = AV_CHANNEL_LAYOUT_STEREO;
            av_channel_layout_copy(&in_ch_layout, &stereo_layout);
        }
    }

    // 设置输出通道布局（固定为立体声）
    AVChannelLayout out_stereo_layout = AV_CHANNEL_LAYOUT_STEREO;
    av_channel_layout_copy(&out_ch_layout, &out_stereo_layout);

    // 使用新的 swr_alloc_set_opts2 函数
    int ret = swr_alloc_set_opts2(&m_swr_ctx,
            &out_ch_layout, m_out_sample_fmt, m_out_sample_rate,
            &in_ch_layout,
            static_cast<AVSampleFormat>(audio_stream->codecpar->format),
            audio_stream->codecpar->sample_rate,
            0, nullptr);

    // 清理临时的 channel layout
    av_channel_layout_uninit(&in_ch_layout);
    av_channel_layout_uninit(&out_ch_layout);

    if (ret < 0) {
        LOGE("Failed to allocate resampler context");
        return false;
    }

    if (!m_swr_ctx || swr_init(m_swr_ctx) < 0) {
        LOGE("Failed to initialize swr context");
        return false;
    }

    m_buffer_size = av_samples_get_buffer_size(nullptr, m_out_channels, SAMPLES_ONE_FRAME, m_out_sample_fmt, 1);
    m_out_buffer = (uint8_t *) av_malloc(m_buffer_size);
    if (!m_out_buffer) {
        LOGE("Failed to allocate out buffer");
        return false;
    }

    return true;
}

void AudioDecoder::start() {
    m_decode_thread = new std::thread(&AudioDecoder::run, this);
}

void AudioDecoder::stop() {
    m_abort = true;
    if (m_packet_queue) {
        m_packet_queue->abort();
    }
    if (m_decode_thread) {
        m_decode_thread->join();
        delete m_decode_thread;
        m_decode_thread = nullptr;
    }
    close();
}

void AudioDecoder::close() {
    if (m_codec_ctx) {
        avcodec_free_context(&m_codec_ctx);
        m_codec_ctx = nullptr;
    }
    if (m_frame) {
        av_frame_free(&m_frame);
        m_frame = nullptr;
    }
    if (m_swr_ctx) {
        swr_free(&m_swr_ctx);
        m_swr_ctx = nullptr;
    }
    if (m_out_buffer) {
        av_free(m_out_buffer);
        m_out_buffer = nullptr;
    }
}

void AudioDecoder::flush() {
    if (m_codec_ctx) {
        avcodec_flush_buffers(m_codec_ctx);
    }
    if (m_ring_buffer) {
        m_ring_buffer->flush();
    }
}

void AudioDecoder::run() {
    LOGD("Audio decoder thread started");
    while (!m_abort) {
        AVPacket* packet = m_packet_queue->pop();
        if (!packet) {
            // Queue is aborted
            break;
        }

        int ret = avcodec_send_packet(m_codec_ctx, packet);
        av_packet_free(&packet);
        if (ret < 0) {
            LOGE("Error sending packet for decoding: %s", get_error_string(ret));
            continue;
        }

        while (!m_abort) {
            ret = avcodec_receive_frame(m_codec_ctx, m_frame);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                break;
            } else if (ret < 0) {
                LOGE("Error during decoding: %s", get_error_string(ret));
                break;
            }

            int max_dst_nb_samples = av_rescale_rnd(m_frame->nb_samples, m_out_sample_rate, m_codec_ctx->sample_rate, AV_ROUND_UP);
            int converted_samples = swr_convert(m_swr_ctx, &m_out_buffer, max_dst_nb_samples, (const uint8_t **) m_frame->data, m_frame->nb_samples);
            if (converted_samples < 0) {
                LOGE("Error during swr_convert");
                continue;
            }

            // 暂时禁用音频速度调整，只使用视频渲染器的时间控制
            // 音频速度调整需要更复杂的算法来保持音质
            int buffer_size = av_samples_get_buffer_size(nullptr, m_out_channels, converted_samples, m_out_sample_fmt, 1);
            m_ring_buffer->write(m_out_buffer, buffer_size);
            av_frame_unref(m_frame);
        }
    }
    LOGD("Audio decoding thread finished.");
}

void AudioDecoder::setSpeed(float speed) {
    if (speed <= 0) return;
    m_speed.store(speed);
    LOGD("Audio decoder speed set to: %f", speed);
}
