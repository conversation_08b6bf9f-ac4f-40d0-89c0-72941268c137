# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: androidplayer
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/Debug/1m454h1f/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target androidplayer


#############################################
# Order-only phony target for androidplayer

build cmake_object_order_depends_target_androidplayer: phony || CMakeFiles/androidplayer.dir

build CMakeFiles/androidplayer.dir/AAudioRender.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/AAudioRender.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\AAudioRender.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir

build CMakeFiles/androidplayer.dir/ANWRender.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/ANWRender.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\ANWRender.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir

build CMakeFiles/androidplayer.dir/native-lib.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/native-lib.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\native-lib.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir

build CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/decoder/YuvDumper.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\decoder\YuvDumper.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\decoder

build CMakeFiles/androidplayer.dir/queue/PacketQueue.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/queue/PacketQueue.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\queue\PacketQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\queue

build CMakeFiles/androidplayer.dir/queue/VideoFrameQueue.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/queue/VideoFrameQueue.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\queue\VideoFrameQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\queue

build CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/demuxer/Demuxer.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\demuxer\Demuxer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\demuxer

build CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/decoder/VideoDecoder.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\decoder\VideoDecoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\decoder

build CMakeFiles/androidplayer.dir/decoder/AudioDecoder.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/decoder/AudioDecoder.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\decoder\AudioDecoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\decoder

build CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o: CXX_COMPILER__androidplayer_Debug D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/render/GLRenderer.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\render\GLRenderer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info  -fPIC -std=c++11
  INCLUDES = -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\render


# =============================================================================
# Link build statements for SHARED_LIBRARY target androidplayer


#############################################
# Link the shared library D:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\build\intermediates\cxx\Debug\1m454h1f\obj\arm64-v8a\libandroidplayer.so

build D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/arm64-v8a/libandroidplayer.so: CXX_SHARED_LIBRARY_LINKER__androidplayer_Debug CMakeFiles/androidplayer.dir/AAudioRender.cpp.o CMakeFiles/androidplayer.dir/ANWRender.cpp.o CMakeFiles/androidplayer.dir/native-lib.cpp.o CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o CMakeFiles/androidplayer.dir/queue/PacketQueue.cpp.o CMakeFiles/androidplayer.dir/queue/VideoFrameQueue.cpp.o CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o CMakeFiles/androidplayer.dir/decoder/AudioDecoder.cpp.o CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o | D$:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/30/liblog.so D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libavutil.so D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libswresample.so D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libswscale.so D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libavcodec.so D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libavformat.so D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libavfilter.so D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a/libavdevice.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O0 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libgcc_real.a -Wl,--exclude-libs,libatomic.a -Wl,--build-id -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/30/liblog.so  -landroid  -laaudio  -lEGL  -lGLESv2  -lavutil  -lswresample  -lswscale  -lavcodec  -lavformat  -lavfilter  -lavdevice  -lz  -latomic -lm
  LINK_PATH = -LD:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/../jniLibs/arm64-v8a
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libandroidplayer.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\build\intermediates\cxx\Debug\1m454h1f\obj\arm64-v8a\libandroidplayer.so
  TARGET_PDB = androidplayer.so.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\.cxx\Debug\1m454h1f\arm64-v8a && D:\APP\Android-studio\Android\SDK\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\.cxx\Debug\1m454h1f\arm64-v8a && D:\APP\Android-studio\Android\SDK\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\src\main\cpp -BD:\idea\VSWorkPlace\androidplayer-11\androidplayer\app\.cxx\Debug\1m454h1f\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build androidplayer: phony D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/arm64-v8a/libandroidplayer.so

build libandroidplayer.so: phony D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/arm64-v8a/libandroidplayer.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/Debug/1m454h1f/arm64-v8a

build all: phony D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/arm64-v8a/libandroidplayer.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/build/cmake/android.toolchain.cmake D$:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/build/cmake/platforms.cmake D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/APP/Android-studio/Android/SDK/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/build/cmake/android.toolchain.cmake D$:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/build/cmake/platforms.cmake D$:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
