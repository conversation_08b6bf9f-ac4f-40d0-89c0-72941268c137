#include "VideoFrameQueue.h"

void VideoFrameQueue::push(VideoFrame* frame) {
    std::lock_guard<std::mutex> lock(mtx);
    if (mAbortRequested) return;
    q.push(frame);
    cv.notify_one();
}

VideoFrame* VideoFrameQueue::pop() {
    std::unique_lock<std::mutex> lock(mtx);
    cv.wait(lock, [this] { return !q.empty() || mAbortRequested; });
    if (mAbortRequested) {
        return nullptr;
    }
    auto* f = q.front(); q.pop();
    return f;
}

void VideoFrameQueue::clear() {
    std::lock_guard<std::mutex> l(mtx);
    while (!q.empty()) {
        auto* f = q.front(); q.pop();
        // 不要手动释放rgba，让析构函数处理
        delete f;
    }
    mAbortRequested = false;
}

void VideoFrameQueue::abort() {
    std::lock_guard<std::mutex> lock(mtx);
    mAbortRequested = true;
    cv.notify_all();
}

bool VideoFrameQueue::empty() { std::lock_guard<std::mutex> l(mtx); return q.empty(); }

size_t VideoFrameQueue::size() { std::lock_guard<std::mutex> l(mtx); return q.size(); }