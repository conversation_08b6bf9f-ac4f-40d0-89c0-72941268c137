{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a/CMakeFiles/3.22.1/CMakeSystem.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a/CMakeFiles/3.22.1/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a/CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/projects/androidplayer/app/.cxx/Debug/4vp86u48/arm64-v8a", "source": "/home/<USER>/projects/androidplayer/app/src/main/cpp"}, "version": {"major": 1, "minor": 0}}