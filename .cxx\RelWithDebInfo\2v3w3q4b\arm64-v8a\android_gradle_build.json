{"buildFiles": ["/home/<USER>/projects/androidplayer/app/src/main/cpp/CMakeLists.txt"], "cleanCommandsComponents": [["/usr/bin/ninja", "-C", "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/2v3w3q4b/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/usr/bin/ninja", "-C", "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/2v3w3q4b/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"androidplayer::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "androidplayer", "output": "/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/2v3w3q4b/obj/arm64-v8a/libandroidplayer.so", "runtimeFiles": ["/home/<USER>/projects/androidplayer/app/src/main/jniLibs/arm64-v8a/libffmpeg.so"]}, "test::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "test", "output": "/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/2v3w3q4b/obj/arm64-v8a/test", "runtimeFiles": ["/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/2v3w3q4b/obj/arm64-v8a/libandroidplayer.so", "/home/<USER>/projects/androidplayer/app/src/main/jniLibs/arm64-v8a/libffmpeg.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}