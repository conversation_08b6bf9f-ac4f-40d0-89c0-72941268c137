#include "VideoPacketQueue.h"
#include <libavutil/mem.h>

VideoPacketQueue::VideoPacketQueue() : mAbort(false) {}

void VideoPacketQueue::push(AVPacket* packet) {
    std::lock_guard<std::mutex> lock(mtx);
    if (mAbort) {
        return; // Aborted, do not push
    }
    q.push(packet);
    cond.notify_one();
}

AVPacket* VideoPacketQueue::pop() {
    std::unique_lock<std::mutex> lock(mtx);
    cond.wait(lock, [this] { return !q.empty() || mAbort; });

    if (mAbort) {
        return nullptr; // Aborted
    }

    AVPacket* pkt = q.front();
    q.pop();
    return pkt;
}

void VideoPacketQueue::clear() {
    std::lock_guard<std::mutex> lock(mtx);
    while (!q.empty()) {
        AVPacket* pkt = q.front();
        q.pop();
        av_packet_free(&pkt);
    }
    mAbort = false; // Reset abort flag
}

void VideoPacketQueue::abort() {
    std::lock_guard<std::mutex> lock(mtx);
    mAbort = true;
    cond.notify_all();
}

bool VideoPacketQueue::empty() {
    std::lock_guard<std::mutex> lock(mtx);
    return q.empty();
}

size_t VideoPacketQueue::size() {
    std::lock_guard<std::mutex> lock(mtx);
    return q.size();
}