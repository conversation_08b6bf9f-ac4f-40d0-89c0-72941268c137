# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: androidplayer
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target androidplayer


#############################################
# Order-only phony target for androidplayer

build cmake_object_order_depends_target_androidplayer: phony || CMakeFiles/androidplayer.dir

build CMakeFiles/androidplayer.dir/AAudioRender.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/AAudioRender.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\AAudioRender.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir

build CMakeFiles/androidplayer.dir/ANWRender.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/ANWRender.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\ANWRender.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir

build CMakeFiles/androidplayer.dir/native-lib.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/native-lib.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\native-lib.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir

build CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/decoder/YuvDumper.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\decoder\YuvDumper.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\decoder

build CMakeFiles/androidplayer.dir/queue/PacketQueue.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/queue/PacketQueue.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\queue\PacketQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\queue

build CMakeFiles/androidplayer.dir/queue/VideoFrameQueue.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/queue/VideoFrameQueue.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\queue\VideoFrameQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\queue

build CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/demuxer/Demuxer.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\demuxer\Demuxer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\demuxer

build CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/decoder/VideoDecoder.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\decoder\VideoDecoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\decoder

build CMakeFiles/androidplayer.dir/decoder/AudioDecoder.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/decoder/AudioDecoder.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\decoder\AudioDecoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\decoder

build CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o: CXX_COMPILER__androidplayer_Debug C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/render/GLRenderer.cpp || cmake_object_order_depends_target_androidplayer
  DEFINES = -Dandroidplayer_EXPORTS
  DEP_FILE = CMakeFiles\androidplayer.dir\render\GLRenderer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  OBJECT_FILE_DIR = CMakeFiles\androidplayer.dir\render


# =============================================================================
# Link build statements for SHARED_LIBRARY target androidplayer


#############################################
# Link the shared library C:\Users\<USER>\Desktop\androidplayer\app\build\intermediates\cxx\Debug\1m454h1f\obj\armeabi-v7a\libandroidplayer.so

build C$:/Users/<USER>/Desktop/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/armeabi-v7a/libandroidplayer.so: CXX_SHARED_LIBRARY_LINKER__androidplayer_Debug CMakeFiles/androidplayer.dir/AAudioRender.cpp.o CMakeFiles/androidplayer.dir/ANWRender.cpp.o CMakeFiles/androidplayer.dir/native-lib.cpp.o CMakeFiles/androidplayer.dir/decoder/YuvDumper.cpp.o CMakeFiles/androidplayer.dir/queue/PacketQueue.cpp.o CMakeFiles/androidplayer.dir/queue/VideoFrameQueue.cpp.o CMakeFiles/androidplayer.dir/demuxer/Demuxer.cpp.o CMakeFiles/androidplayer.dir/decoder/VideoDecoder.cpp.o CMakeFiles/androidplayer.dir/decoder/AudioDecoder.cpp.o CMakeFiles/androidplayer.dir/render/GLRenderer.cpp.o | C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/30/liblog.so C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/../jniLibs/armeabi-v7a/libffmpeg.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libgcc_real.a -Wl,--exclude-libs,libatomic.a -Wl,--build-id -Wl,--fatal-warnings -Wl,--exclude-libs,libunwind.a -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/30/liblog.so  -landroid  -laaudio  -lEGL  -lGLESv2  -Wl,--whole-archive  C:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/../jniLibs/armeabi-v7a/libffmpeg.so  -Wl,--no-whole-archive  -latomic -lm
  OBJECT_DIR = CMakeFiles\androidplayer.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libandroidplayer.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\Desktop\androidplayer\app\build\intermediates\cxx\Debug\1m454h1f\obj\armeabi-v7a\libandroidplayer.so
  TARGET_PDB = androidplayer.so.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Desktop\androidplayer\app\.cxx\Debug\1m454h1f\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Desktop\androidplayer\app\.cxx\Debug\1m454h1f\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Desktop\androidplayer\app\src\main\cpp -BC:\Users\<USER>\Desktop\androidplayer\app\.cxx\Debug\1m454h1f\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build androidplayer: phony C$:/Users/<USER>/Desktop/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/armeabi-v7a/libandroidplayer.so

build libandroidplayer.so: phony C$:/Users/<USER>/Desktop/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/armeabi-v7a/libandroidplayer.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/androidplayer/app/.cxx/Debug/1m454h1f/armeabi-v7a

build all: phony C$:/Users/<USER>/Desktop/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/armeabi-v7a/libandroidplayer.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/build/cmake/platforms.cmake C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/build/cmake/platforms.cmake C$:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
