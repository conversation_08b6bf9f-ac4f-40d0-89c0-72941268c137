#ifndef ANDROIDPLAYER_VIDEODECODER_H
#define ANDROIDPLAYER_VIDEODECODER_H

#include "../queue/VideoFrameQueue.h"
#include "../queue/PacketQueue.h"

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/frame.h>
#include <libswscale/swscale.h>
}

#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>

class VideoDecoder {
public:
    VideoDecoder();
    ~VideoDecoder();

    int open(AVStream* stream, PacketQueue* packetQueue, VideoFrameQueue* frameQueue);
    void start();
    void stop();
    void flush();
    void pause();
    void resume();
    void setSpeed(float speed);

private:
    void run();

    AVCodecContext* mCodecCtx = nullptr;
    PacketQueue* mPacketQueue = nullptr;
    VideoFrameQueue* mFrameQueue = nullptr;
    AVFrame* mFrame = nullptr;
    SwsContext* mSwsCtx = nullptr;
    std::thread* mDecodeThread = nullptr;
    volatile bool mAbort = false;
    volatile bool mPaused = false;
    std::atomic<float> mSpeed{1.0f};
};

#endif //ANDROIDPLAYER_VIDEODECODER_H