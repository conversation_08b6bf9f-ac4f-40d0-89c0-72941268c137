{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Determine.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Determine.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a/CMakeFiles/3.22.1/CMakeSystem.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Determine-Compiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/<PERSON>-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a/CMakeFiles/3.22.1/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a/CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Clang.cmake"}, {"isExternal": true, "path": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/flags.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a/CMakeFiles/3.22.1/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a/CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/4m444y38/arm64-v8a", "source": "/home/<USER>/projects/androidplayer/app/src/main/cpp"}, "version": {"major": 1, "minor": 0}}