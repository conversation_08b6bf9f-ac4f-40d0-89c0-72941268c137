[{"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\AAudioRender.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\AAudioRender.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\AAudioRender.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\ANWRender.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\ANWRender.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\ANWRender.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\native-lib.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\native-lib.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\native-lib.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\decoder\\YuvDumper.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\decoder\\YuvDumper.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\decoder\\YuvDumper.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\queue\\PacketQueue.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\queue\\PacketQueue.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\queue\\PacketQueue.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\queue\\VideoFrameQueue.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\queue\\VideoFrameQueue.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\queue\\VideoFrameQueue.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\demuxer\\Demuxer.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\demuxer\\Demuxer.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\demuxer\\Demuxer.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\decoder\\VideoDecoder.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\decoder\\VideoDecoder.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\decoder\\VideoDecoder.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\decoder\\AudioDecoder.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\decoder\\AudioDecoder.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\decoder\\AudioDecoder.cpp"}, {"directory": "D:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/.cxx/RelWithDebInfo/5v2y2j1a/arm64-v8a", "command": "D:\\APP\\Android-studio\\Android\\SDK\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android30 --gcc-toolchain=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64 --sysroot=D:/APP/Android-studio/Android/SDK/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS -Dandroidplayer_EXPORTS -ID:/idea/VSWorkPlace/androidplayer-11/androidplayer/app/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -std=c++11 -fexceptions -O2 -g -DNDEBUG -fPIC -std=c++11 -o CMakeFiles\\androidplayer.dir\\render\\GLRenderer.cpp.o -c D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\render\\GLRenderer.cpp", "file": "D:\\idea\\VSWorkPlace\\androidplayer-11\\androidplayer\\app\\src\\main\\cpp\\render\\GLRenderer.cpp"}]