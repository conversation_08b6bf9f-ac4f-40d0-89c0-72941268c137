{"artifacts": [{"path": "C:/Users/<USER>/Desktop/androidplayer/app/build/intermediates/cxx/Debug/1m454h1f/obj/armeabi-v7a/libandroidplayer.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 18, "parent": 0}, {"command": 1, "file": 0, "line": 32, "parent": 0}, {"command": 2, "file": 0, "line": 7, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC"}], "defines": [{"define": "androidplayer_EXPORTS"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Desktop/androidplayer/app/src/main/cpp/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "androidplayer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libgcc_real.a -Wl,--exclude-libs,libatomic.a -Wl,--build-id -Wl,--fatal-warnings -Wl,--exclude-libs,libunwind.a -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.3.6528147\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\30\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "-<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 2, "fragment": "-lGLESv2", "role": "libraries"}, {"backtrace": 2, "fragment": "-Wl,--whole-archive", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\Desktop\\androidplayer\\app\\src\\main\\cpp\\..\\jniLibs\\armeabi-v7a\\libffmpeg.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-Wl,--no-whole-archive", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/21.3.6528147/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "androidplayer", "nameOnDisk": "libandroidplayer.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "AAudioRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ANWRender.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "native-lib.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/YuvDumper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "queue/PacketQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "queue/VideoFrameQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "demuxer/Demuxer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/VideoDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "decoder/AudioDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "render/GLRenderer.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}