                        -H/home/<USER>/projects/androidplayer/app/src/main/cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=30
-DANDROID_PLATFORM=android-30
-D<PERSON>DROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/21.3.6528147
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/21.3.6528147
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/21.3.6528147/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/usr/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/2v3w3q4b/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/projects/androidplayer/app/build/intermediates/cxx/RelWithDebInfo/2v3w3q4b/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/home/<USER>/projects/androidplayer/app/.cxx/RelWithDebInfo/2v3w3q4b/arm64-v8a
-GNinja
                        Build command args: []
                        Version: 2